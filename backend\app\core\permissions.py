"""Permission and role-based access control system."""
from typing import List, Dict, Set
from functools import wraps
from fastapi import <PERSON><PERSON><PERSON>Ex<PERSON>, status

from app.core.constants import UserRole, Permission
from app.models.user import User


class PermissionManager:
    """Manages role-based permissions."""
    
    # Define role hierarchy (higher roles inherit lower role permissions)
    ROLE_HIERARCHY = {
        UserRole.USER: 0,
        UserRole.MODERATOR: 1,
        UserRole.ADMIN: 2,
    }
    
    # Define permissions for each role
    ROLE_PERMISSIONS = {
        UserRole.USER: {
            Permission.READ,
        },
        UserRole.MODERATOR: {
            Permission.READ,
            Permission.WRITE,
        },
        UserRole.ADMIN: {
            Permission.READ,
            Permission.WRITE,
            Permission.DELETE,
            Permission.ADMIN,
        },
    }
    
    @classmethod
    def get_user_permissions(cls, user: User) -> Set[str]:
        """Get all permissions for a user based on their role."""
        if user.is_superuser:
            # Superusers have all permissions
            return set(Permission.__dict__.values())
        
        user_role_level = cls.ROLE_HIERARCHY.get(user.role, 0)
        permissions = set()
        
        # Add permissions from current role and all lower roles
        for role, level in cls.ROLE_HIERARCHY.items():
            if level <= user_role_level:
                permissions.update(cls.ROLE_PERMISSIONS.get(role, set()))
        
        return permissions
    
    @classmethod
    def user_has_permission(cls, user: User, permission: str) -> bool:
        """Check if user has a specific permission."""
        if user.is_superuser:
            return True
        
        user_permissions = cls.get_user_permissions(user)
        return permission in user_permissions
    
    @classmethod
    def user_has_role(cls, user: User, required_role: str) -> bool:
        """Check if user has required role or higher."""
        if user.is_superuser:
            return True
        
        user_role_level = cls.ROLE_HIERARCHY.get(user.role, 0)
        required_role_level = cls.ROLE_HIERARCHY.get(required_role, 999)
        
        return user_role_level >= required_role_level
    
    @classmethod
    def user_can_access_resource(cls, user: User, resource_owner_id: str, required_permission: str) -> bool:
        """Check if user can access a resource (owns it or has permission)."""
        # User can access their own resources
        if str(user.id) == resource_owner_id:
            return True
        
        # Or if they have the required permission
        return cls.user_has_permission(user, required_permission)


def require_permissions(*permissions: str):
    """Decorator to require specific permissions."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract current_user from kwargs
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Check permissions
            for permission in permissions:
                if not PermissionManager.user_has_permission(current_user, permission):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"Permission '{permission}' required"
                    )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_roles(*roles: str):
    """Decorator to require specific roles."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract current_user from kwargs
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Check if user has any of the required roles
            has_role = any(
                PermissionManager.user_has_role(current_user, role) 
                for role in roles
            )
            
            if not has_role:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"One of these roles required: {', '.join(roles)}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_ownership_or_permission(permission: str):
    """Decorator to require resource ownership or specific permission."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Try to extract resource owner ID from various possible parameter names
            resource_owner_id = (
                kwargs.get('owner_id') or 
                kwargs.get('user_id') or 
                getattr(kwargs.get('item'), 'owner_id', None) or
                getattr(kwargs.get('resource'), 'owner_id', None)
            )
            
            if resource_owner_id:
                if not PermissionManager.user_can_access_resource(
                    current_user, str(resource_owner_id), permission
                ):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Access denied: insufficient permissions"
                    )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


# Permission manager instance
permission_manager = PermissionManager()
