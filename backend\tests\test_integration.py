"""Integration tests for complete workflows."""
import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.models.user import User
from app.crud.user import user_crud
from app.crud.session import session_crud
from app.crud.email_verification import email_verification_crud


class TestAuthenticationFlow:
    """Test complete authentication workflows."""
    
    def test_complete_registration_and_login_flow(self, client: TestClient):
        """Test complete user registration and login flow."""
        # 1. Register new user
        user_data = {
            "email": "<EMAIL>",
            "password": "integrationtest123",
            "full_name": "Integration Test User"
        }
        
        register_response = client.post(
            f"{settings.API_V1_STR}/auth/register",
            json=user_data
        )
        
        assert register_response.status_code == 200
        user_info = register_response.json()
        assert user_info["email"] == user_data["email"]
        assert user_info["is_verified"] is False
        
        # 2. Login with new user
        login_data = {
            "username": user_data["email"],
            "password": user_data["password"]
        }
        
        login_response = client.post(
            f"{settings.API_V1_STR}/auth/login",
            data=login_data
        )
        
        assert login_response.status_code == 200
        tokens = login_response.json()
        assert "access_token" in tokens
        assert "refresh_token" in tokens
        
        # 3. Access protected endpoint
        headers = {"Authorization": f"Bearer {tokens['access_token']}"}
        profile_response = client.get(
            f"{settings.API_V1_STR}/users/me",
            headers=headers
        )
        
        assert profile_response.status_code == 200
        profile = profile_response.json()
        assert profile["email"] == user_data["email"]
        
        # 4. Refresh token
        refresh_response = client.post(
            f"{settings.API_V1_STR}/auth/refresh",
            json={"refresh_token": tokens["refresh_token"]}
        )
        
        assert refresh_response.status_code == 200
        new_tokens = refresh_response.json()
        assert "access_token" in new_tokens
        
        # 5. Logout
        logout_response = client.post(
            f"{settings.API_V1_STR}/auth/logout",
            headers=headers
        )
        
        assert logout_response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_email_verification_flow(self, client: TestClient, async_db: AsyncSession):
        """Test email verification workflow."""
        # 1. Register user
        user_data = {
            "email": "<EMAIL>",
            "password": "verifytest123",
            "full_name": "Verify Test User"
        }
        
        register_response = client.post(
            f"{settings.API_V1_STR}/auth/register",
            json=user_data
        )
        
        assert register_response.status_code == 200
        user_info = register_response.json()
        user_id = user_info["id"]
        
        # 2. Create verification token (simulating email service)
        verification = await email_verification_crud.create_verification(
            async_db,
            user_id=user_id,
            email=user_data["email"]
        )
        
        # 3. Verify email
        verify_response = client.post(
            f"{settings.API_V1_STR}/auth/verify-email",
            params={"token": verification.token}
        )
        
        assert verify_response.status_code == 200
        
        # 4. Check user is verified
        user = await user_crud.get_async(async_db, id=user_id)
        assert user.is_verified is True
        
        # 5. Try to verify again (should fail)
        verify_again_response = client.post(
            f"{settings.API_V1_STR}/auth/verify-email",
            params={"token": verification.token}
        )
        
        assert verify_again_response.status_code == 400
    
    def test_session_management_flow(self, client: TestClient, test_user: User):
        """Test session management workflow."""
        # 1. Login to create session
        login_data = {
            "username": test_user.email,
            "password": "testpassword123"
        }
        
        login_response = client.post(
            f"{settings.API_V1_STR}/auth/login",
            data=login_data
        )
        
        assert login_response.status_code == 200
        tokens = login_response.json()
        headers = {"Authorization": f"Bearer {tokens['access_token']}"}
        
        # 2. Access protected endpoint
        profile_response = client.get(
            f"{settings.API_V1_STR}/users/me",
            headers=headers
        )
        
        assert profile_response.status_code == 200
        
        # 3. Logout from all devices
        logout_all_response = client.post(
            f"{settings.API_V1_STR}/auth/logout-all",
            headers=headers
        )
        
        assert logout_all_response.status_code == 200
        
        # 4. Try to access with old token (should fail after logout-all)
        # Note: This might still work depending on JWT implementation
        # In a real scenario, you'd check session invalidation


class TestUserManagementFlow:
    """Test user management workflows."""
    
    def test_admin_user_management_flow(self, client: TestClient, admin_user: User):
        """Test admin user management workflow."""
        # 1. Login as admin
        login_data = {
            "username": admin_user.email,
            "password": "adminpassword123"
        }
        
        login_response = client.post(
            f"{settings.API_V1_STR}/auth/login",
            data=login_data
        )
        
        assert login_response.status_code == 200
        tokens = login_response.json()
        admin_headers = {"Authorization": f"Bearer {tokens['access_token']}"}
        
        # 2. Create new user as admin
        new_user_data = {
            "email": "<EMAIL>",
            "password": "manageduser123",
            "full_name": "Managed User"
        }
        
        create_response = client.post(
            f"{settings.API_V1_STR}/auth/register",
            json=new_user_data
        )
        
        assert create_response.status_code == 200
        new_user = create_response.json()
        new_user_id = new_user["id"]
        
        # 3. Get all users as admin
        users_response = client.get(
            f"{settings.API_V1_STR}/users/",
            headers=admin_headers
        )
        
        assert users_response.status_code == 200
        users = users_response.json()
        assert len(users) >= 2  # At least admin and new user
        
        # 4. Update user as admin
        update_data = {
            "full_name": "Updated Managed User",
            "role": "moderator"
        }
        
        update_response = client.put(
            f"{settings.API_V1_STR}/users/{new_user_id}",
            json=update_data,
            headers=admin_headers
        )
        
        assert update_response.status_code == 200
        updated_user = update_response.json()
        assert updated_user["full_name"] == update_data["full_name"]
        assert updated_user["role"] == update_data["role"]
        
        # 5. Get specific user as admin
        get_user_response = client.get(
            f"{settings.API_V1_STR}/users/{new_user_id}",
            headers=admin_headers
        )
        
        assert get_user_response.status_code == 200
        user_details = get_user_response.json()
        assert user_details["id"] == new_user_id
        
        # 6. Delete user as admin
        delete_response = client.delete(
            f"{settings.API_V1_STR}/users/{new_user_id}",
            headers=admin_headers
        )
        
        assert delete_response.status_code == 200
        
        # 7. Verify user is deleted
        get_deleted_response = client.get(
            f"{settings.API_V1_STR}/users/{new_user_id}",
            headers=admin_headers
        )
        
        assert get_deleted_response.status_code == 404
    
    def test_user_profile_management_flow(self, client: TestClient, test_user: User):
        """Test user profile management workflow."""
        # 1. Login
        login_data = {
            "username": test_user.email,
            "password": "testpassword123"
        }
        
        login_response = client.post(
            f"{settings.API_V1_STR}/auth/login",
            data=login_data
        )
        
        assert login_response.status_code == 200
        tokens = login_response.json()
        headers = {"Authorization": f"Bearer {tokens['access_token']}"}
        
        # 2. Get current profile
        profile_response = client.get(
            f"{settings.API_V1_STR}/users/me",
            headers=headers
        )
        
        assert profile_response.status_code == 200
        original_profile = profile_response.json()
        
        # 3. Update profile
        update_data = {
            "full_name": "Updated Test User",
            "bio": "This is my updated bio"
        }
        
        update_response = client.put(
            f"{settings.API_V1_STR}/users/me",
            json=update_data,
            headers=headers
        )
        
        assert update_response.status_code == 200
        updated_profile = update_response.json()
        assert updated_profile["full_name"] == update_data["full_name"]
        assert updated_profile["bio"] == update_data["bio"]
        assert updated_profile["email"] == original_profile["email"]  # Email unchanged
        
        # 4. Verify changes persist
        verify_response = client.get(
            f"{settings.API_V1_STR}/users/me",
            headers=headers
        )
        
        assert verify_response.status_code == 200
        verified_profile = verify_response.json()
        assert verified_profile["full_name"] == update_data["full_name"]
        assert verified_profile["bio"] == update_data["bio"]


class TestErrorHandlingFlow:
    """Test error handling in workflows."""
    
    def test_invalid_credentials_flow(self, client: TestClient):
        """Test handling of invalid credentials."""
        # 1. Try to login with non-existent user
        login_data = {
            "username": "<EMAIL>",
            "password": "wrongpassword"
        }
        
        login_response = client.post(
            f"{settings.API_V1_STR}/auth/login",
            data=login_data
        )
        
        assert login_response.status_code == 401
        
        # 2. Try to access protected endpoint without token
        profile_response = client.get(f"{settings.API_V1_STR}/users/me")
        
        assert profile_response.status_code == 401
        
        # 3. Try to access protected endpoint with invalid token
        invalid_headers = {"Authorization": "Bearer invalid_token"}
        profile_response = client.get(
            f"{settings.API_V1_STR}/users/me",
            headers=invalid_headers
        )
        
        assert profile_response.status_code == 401
    
    def test_permission_denied_flow(self, client: TestClient, test_user: User, admin_user: User):
        """Test permission denied scenarios."""
        # 1. Login as regular user
        login_data = {
            "username": test_user.email,
            "password": "testpassword123"
        }
        
        login_response = client.post(
            f"{settings.API_V1_STR}/auth/login",
            data=login_data
        )
        
        assert login_response.status_code == 200
        tokens = login_response.json()
        user_headers = {"Authorization": f"Bearer {tokens['access_token']}"}
        
        # 2. Try to access admin-only endpoint
        users_response = client.get(
            f"{settings.API_V1_STR}/users/",
            headers=user_headers
        )
        
        assert users_response.status_code == 403
        
        # 3. Try to access other user's profile
        other_user_response = client.get(
            f"{settings.API_V1_STR}/users/{admin_user.id}",
            headers=user_headers
        )
        
        assert other_user_response.status_code == 403
        
        # 4. Try to update other user
        update_data = {"full_name": "Unauthorized Update"}
        update_response = client.put(
            f"{settings.API_V1_STR}/users/{admin_user.id}",
            json=update_data,
            headers=user_headers
        )
        
        assert update_response.status_code == 403
