// Demonstration of OpenAPI type checking mechanism
import { generateTypeSafetyReport, logTypeSafetyReport } from "./type-validation";
import { typedApiClient } from "./typed-api-client";
import type { components } from "@/types/api";

// Demo function to show type checking in action
export function demonstrateTypeChecking() {
  console.log("🔍 OpenAPI Type Checking Demonstration");
  console.log("=====================================");
  
  // 1. Generate and display type safety report
  const report = generateTypeSafetyReport();
  
  console.log("\n📊 Type Safety Report:");
  console.log(`Overall Status: ${report.overall ? "✅ PASS" : "❌ ISSUES FOUND"}`);
  
  console.log("\n✅ Compatible Types:");
  Object.entries(report.reports)
    .filter(([_, r]) => r.compatible)
    .forEach(([name, _]) => console.log(`  • ${name}`));
  
  console.log("\n❌ Types with Issues:");
  Object.entries(report.reports)
    .filter(([_, r]) => !r.compatible)
    .forEach(([name, report]) => {
      console.log(`  • ${name}:`);
      report.issues.forEach(issue => console.log(`    - ${issue}`));
    });
  
  console.log("\n💡 Recommendations:");
  Object.values(report.reports)
    .flatMap(r => r.recommendations)
    .forEach(rec => console.log(`  • ${rec}`));
  
  // 2. Demonstrate type-safe API calls
  console.log("\n🔧 Type-Safe API Client Examples:");
  demonstrateTypedApiCalls();
  
  // 3. Show type validation in action
  console.log("\n🛡️ Runtime Type Validation Examples:");
  demonstrateRuntimeValidation();
  
  // 4. Show compile-time type safety
  console.log("\n⚡ Compile-Time Type Safety:");
  demonstrateCompileTimeTypeSafety();
}

function demonstrateTypedApiCalls() {
  // These examples show how the typed API client ensures type safety
  
  console.log("  ✓ Register user (type-safe request/response):");
  console.log("    const user = await typedApiClient.register({");
  console.log("      email: '<EMAIL>',");
  console.log("      password: 'password123',");
  console.log("      full_name: 'John Doe' // Optional field");
  console.log("    });");
  console.log("    // Response is automatically typed as UserResponse");
  
  console.log("\n  ✓ Login user (form data conversion):");
  console.log("    const tokens = await typedApiClient.login({");
  console.log("      email: '<EMAIL>',");
  console.log("      password: 'password123'");
  console.log("    });");
  console.log("    // Response is automatically typed as Token");
  
  console.log("\n  ✓ Get current user (authenticated request):");
  console.log("    const user = await typedApiClient.getCurrentUser();");
  console.log("    // Response is automatically typed as UserResponse");
  console.log("    // TypeScript knows: user.id, user.email, user.role, etc.");
}

function demonstrateRuntimeValidation() {
  // Show runtime validation examples
  
  const validUser = {
    id: "123e4567-e89b-12d3-a456-************",
    email: "<EMAIL>",
    full_name: "Test User",
    is_active: true,
    is_verified: true,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    role: "user",
  };
  
  const invalidUser = {
    id: "invalid",
    email: "not-an-email",
    // missing required fields
  };
  
  console.log("  ✓ Valid user data:");
  console.log(`    isValidUser(validUser) = true`);
  
  console.log("  ✗ Invalid user data:");
  console.log(`    isValidUser(invalidUser) = false`);
  
  console.log("  💡 Runtime validation catches:");
  console.log("    - Missing required fields");
  console.log("    - Incorrect data types");
  console.log("    - Invalid format (email, UUID, etc.)");
}

function demonstrateCompileTimeTypeSafety() {
  // These examples show compile-time type safety
  
  console.log("  ✓ TypeScript catches errors at compile time:");
  console.log("");
  
  console.log("  // ❌ This would cause a TypeScript error:");
  console.log("  // const user: UserResponse = {");
  console.log("  //   id: 123, // ❌ Should be string");
  console.log("  //   email: '<EMAIL>',");
  console.log("  //   // ❌ Missing required fields");
  console.log("  // };");
  console.log("");
  
  console.log("  // ✅ This is type-safe:");
  console.log("  // const user: UserResponse = {");
  console.log("  //   id: '123e4567-e89b-12d3-a456-************',");
  console.log("  //   email: '<EMAIL>',");
  console.log("  //   full_name: 'Test User',");
  console.log("  //   is_active: true,");
  console.log("  //   is_verified: true,");
  console.log("  //   created_at: '2024-01-01T00:00:00Z',");
  console.log("  //   updated_at: '2024-01-01T00:00:00Z',");
  console.log("  //   role: 'user'");
  console.log("  // };");
}

// Type comparison function to show differences
export function compareManualVsOpenAPITypes() {
  console.log("\n🔄 Manual Types vs OpenAPI Types Comparison:");
  console.log("============================================");
  
  console.log("\n📝 Manual User Type:");
  console.log("interface User {");
  console.log("  id: string;");
  console.log("  email: string;");
  console.log("  full_name?: string;");
  console.log("  is_active: boolean;");
  console.log("  is_verified: boolean;");
  console.log("  created_at: string;");
  console.log("  updated_at: string;        // ❌ Required");
  console.log("  // ❌ Missing 'role' field");
  console.log("}");
  
  console.log("\n🔧 OpenAPI UserResponse Type:");
  console.log("interface UserResponse {");
  console.log("  id: string;");
  console.log("  email: string;");
  console.log("  full_name?: string | null;");
  console.log("  is_active: boolean;");
  console.log("  is_verified: boolean;");
  console.log("  created_at: string;");
  console.log("  updated_at?: string | null; // ✅ Optional");
  console.log("  role: string;               // ✅ Present");
  console.log("}");
  
  console.log("\n🎯 Key Differences:");
  console.log("  1. updated_at: Required vs Optional");
  console.log("  2. Missing role field in manual type");
  console.log("  3. Null handling: OpenAPI allows null values");
  console.log("  4. Date format: OpenAPI specifies date-time format");
}

// Function to check if backend schema has changed
export async function checkSchemaChanges(): Promise<boolean> {
  try {
    console.log("\n🔄 Checking for Backend Schema Changes...");
    
    // Fetch current OpenAPI schema
    const response = await fetch("http://localhost:8000/api/v1/openapi.json");
    const currentSchema = await response.json();
    
    // Check if key endpoints exist
    const requiredEndpoints = [
      "/api/v1/auth/register",
      "/api/v1/auth/login",
      "/api/v1/auth/verify-email",
      "/api/v1/users/me"
    ];
    
    const missingEndpoints = requiredEndpoints.filter(
      endpoint => !currentSchema.paths[endpoint]
    );
    
    if (missingEndpoints.length > 0) {
      console.log("❌ Missing endpoints:", missingEndpoints);
      return false;
    }
    
    // Check if key schemas exist
    const requiredSchemas = [
      "UserResponse",
      "UserCreate", 
      "Token",
      "HTTPValidationError"
    ];
    
    const missingSchemas = requiredSchemas.filter(
      schema => !currentSchema.components?.schemas?.[schema]
    );
    
    if (missingSchemas.length > 0) {
      console.log("❌ Missing schemas:", missingSchemas);
      return false;
    }
    
    console.log("✅ Backend schema is up to date");
    return true;
  } catch (error) {
    console.log("❌ Failed to check schema:", error);
    return false;
  }
}

// Export for use in development
export { logTypeSafetyReport };
