"use client";

import { useActionState, useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import {
  GalleryVerticalEnd,
  Loader2,
  Eye,
  EyeOff,
  AlertCircle,
  Check,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { loginAction } from "@/lib/actions/auth-actions";
import { useAuth } from "@/context/auth-context";
import Link from "next/link";
import type { FormState } from "@/types";

const initialState: FormState = {
  success: false,
  message: "",
  errors: {},
};

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [state, formAction, isPending] = useActionState(
    loginAction,
    initialState
  );
  const { login } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectUrl = searchParams.get("redirect") || "/dashboard";
  const [showPassword, setShowPassword] = useState(false);

  // Helper function to get user-friendly error messages
  const getFieldError = (fieldName: string): string | undefined => {
    if (state.errors?.[fieldName]) {
      return state.errors[fieldName][0];
    }
    return undefined;
  };

  // Handle success messages from URL params
  useEffect(() => {
    const message = searchParams.get("message");
    if (message === "signup-success") {
      toast.success("Welcome! Please sign in with your new account.", {
        description: "Don't forget to verify your email address.",
        duration: 5000,
      });
      // Clean up the URL parameter
      router.replace("/login");
    } else if (message === "email-verified") {
      toast.success("Email verified successfully!", {
        description: "You can now sign in to your account.",
        duration: 5000,
      });
      // Clean up the URL parameter
      router.replace("/login");
    } else if (message === "resend-verification") {
      toast.info("Need a new verification email?", {
        description:
          "Contact support or try signing up again if you haven't received the verification email.",
        duration: 7000,
      });
      // Clean up the URL parameter
      router.replace("/login");
    } else if (message === "password-reset-success") {
      toast.success("Password updated successfully!", {
        description: "You can now sign in with your new password.",
        duration: 5000,
      });
      // Clean up the URL parameter
      router.replace("/login");
    }
  }, [searchParams, router]);

  // Handle successful login with improved UX
  useEffect(() => {
    if (state.success && state.tokens) {
      toast.success(state.message || "Welcome back! Login successful.", {
        description: "Redirecting to your dashboard...",
        duration: 3000,
      });

      // Store tokens in localStorage and update auth context
      localStorage.setItem("auth_tokens", JSON.stringify(state.tokens));
      login(state.tokens);

      // Small delay to show the success message before redirect
      setTimeout(() => {
        router.push(redirectUrl);
      }, 1500);
    }
  }, [state.success, state.tokens, state.message, login, router, redirectUrl]);

  // Handle form errors with better user feedback
  useEffect(() => {
    if (!state.success && state.message && state.message !== "") {
      // Don't show toast for validation errors, only for server errors
      if (!state.errors || Object.keys(state.errors).length === 0) {
        toast.error(state.message);
      }
    }
  }, [state.success, state.message, state.errors]);

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <form action={formAction}>
        <div className="flex flex-col gap-6">
          <div className="flex flex-col items-center gap-2">
            <Link
              href="/"
              className="flex flex-col items-center gap-2 font-medium"
            >
              <div className="flex size-8 items-center justify-center rounded-md bg-primary text-primary-foreground">
                <GalleryVerticalEnd className="size-6" />
              </div>
              <span className="sr-only">WebApp</span>
            </Link>
            <h1 className="text-xl font-bold">Welcome back</h1>
            <div className="text-center text-sm">
              Don&apos;t have an account?{" "}
              <Link href="/signup" className="underline underline-offset-4">
                Sign up
              </Link>
            </div>
          </div>

          {!state.success && state.message && !state.errors && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{state.message}</AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col gap-6">
            <div className="grid gap-3">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                required
                disabled={isPending}
                className={cn(
                  getFieldError("email") &&
                    "border-destructive focus-visible:ring-destructive"
                )}
              />
              {getFieldError("email") && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {getFieldError("email")}
                </p>
              )}
            </div>

            <div className="grid gap-3">
              <div className="flex items-center">
                <Label htmlFor="password">Password</Label>
                <Link
                  href="/forgot-password"
                  className="ml-auto inline-block text-sm underline-offset-4 hover:underline"
                >
                  Forgot password?
                </Link>
              </div>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  required
                  disabled={isPending}
                  className={cn(
                    "pr-10",
                    getFieldError("password") &&
                      "border-destructive focus-visible:ring-destructive"
                  )}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isPending}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showPassword ? "Hide password" : "Show password"}
                  </span>
                </Button>
              </div>
              {getFieldError("password") && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {getFieldError("password")}
                </p>
              )}
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={isPending || state.success}
            >
              {state.success ? (
                <>
                  <Check className="mr-2 h-4 w-4" />
                  Welcome back! Redirecting...
                </>
              ) : isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Signing in...
                </>
              ) : (
                "Sign In"
              )}
            </Button>
          </div>
        </div>
      </form>

      <div className="text-muted-foreground text-center text-xs text-balance">
        By signing in, you agree to our{" "}
        <Link
          href="/terms"
          className="underline underline-offset-4 hover:text-primary"
        >
          Terms of Service
        </Link>{" "}
        and{" "}
        <Link
          href="/privacy"
          className="underline underline-offset-4 hover:text-primary"
        >
          Privacy Policy
        </Link>
        .
      </div>
    </div>
  );
}
