#!/usr/bin/env python3
"""Test script for cache API integration."""
import asyncio
import json
import sys
from typing import Dict, Any

import httpx
from app.core.cache import cache


async def test_cache_api_integration():
    """Test the cache API endpoints."""
    print("🧪 Testing Cache API Integration")
    print("=" * 50)
    
    # Test data
    base_url = "http://localhost:8000/api/v1"
    test_user_credentials = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    async with httpx.AsyncClient() as client:
        try:
            # 1. Login to get access token
            print("1. 🔐 Logging in to get access token...")
            login_response = await client.post(
                f"{base_url}/auth/login",
                json=test_user_credentials
            )
            
            if login_response.status_code != 200:
                print(f"❌ Login failed: {login_response.status_code}")
                print(f"Response: {login_response.text}")
                return False
            
            tokens = login_response.json()
            access_token = tokens["access_token"]
            headers = {"Authorization": f"Bearer {access_token}"}
            print("✅ Login successful")
            
            # 2. Test cache health check
            print("\n2. 🏥 Testing cache health check...")
            health_response = await client.get(f"{base_url}/cache/health")
            
            if health_response.status_code == 200:
                health_data = health_response.json()
                print(f"✅ Cache health: {health_data['status']}")
                print(f"   Response time: {health_data['response_time_ms']}ms")
            else:
                print(f"❌ Health check failed: {health_response.status_code}")
            
            # 3. Test setting cache items
            print("\n3. 💾 Testing cache set operations...")
            test_data = {
                "user_preferences": {
                    "theme": "dark",
                    "language": "en",
                    "notifications": True
                },
                "current_user": {
                    "id": "123",
                    "name": "Test User",
                    "email": "<EMAIL>"
                },
                "app_settings": {
                    "version": "1.0.0",
                    "last_updated": "2024-01-01"
                }
            }
            
            for key, data in test_data.items():
                cache_request = {
                    "data": data,
                    "ttl": 600  # 10 minutes
                }
                
                set_response = await client.put(
                    f"{base_url}/cache/{key}",
                    json=cache_request,
                    headers=headers
                )
                
                if set_response.status_code == 201:
                    print(f"✅ Set cache item: {key}")
                else:
                    print(f"❌ Failed to set {key}: {set_response.status_code}")
                    print(f"   Response: {set_response.text}")
            
            # 4. Test getting cache items
            print("\n4. 📖 Testing cache get operations...")
            for key in test_data.keys():
                get_response = await client.get(
                    f"{base_url}/cache/{key}",
                    headers=headers
                )
                
                if get_response.status_code == 200:
                    cached_data = get_response.json()
                    print(f"✅ Retrieved cache item: {key}")
                    print(f"   TTL: {cached_data.get('ttl', 'N/A')} seconds")
                else:
                    print(f"❌ Failed to get {key}: {get_response.status_code}")
            
            # 5. Test cache key info
            print("\n5. ℹ️  Testing cache key info...")
            info_response = await client.get(
                f"{base_url}/cache/info/user_preferences",
                headers=headers
            )
            
            if info_response.status_code == 200:
                info_data = info_response.json()
                print(f"✅ Cache key info retrieved")
                print(f"   Exists: {info_data['exists']}")
                print(f"   TTL: {info_data.get('ttl', 'N/A')} seconds")
                print(f"   Size: {info_data.get('size', 'N/A')} bytes")
            else:
                print(f"❌ Failed to get key info: {info_response.status_code}")
            
            # 6. Test bulk cache operations
            print("\n6. 📦 Testing bulk cache operations...")
            bulk_data = {
                "session_data": {
                    "data": {"session_id": "abc123", "expires": "2024-12-31"},
                    "ttl": 300
                },
                "temp_data": {
                    "data": {"temp": "value", "timestamp": 1234567890},
                    "ttl": 60
                }
            }
            
            bulk_response = await client.post(
                f"{base_url}/cache/bulk",
                json={"items": bulk_data},
                headers=headers
            )
            
            if bulk_response.status_code == 200:
                bulk_result = bulk_response.json()
                print(f"✅ Bulk operation completed")
                print(f"   Success: {bulk_result['success_count']}")
                print(f"   Failed: {bulk_result['failed_count']}")
            else:
                print(f"❌ Bulk operation failed: {bulk_response.status_code}")
            
            # 7. Test cache search
            print("\n7. 🔍 Testing cache search...")
            search_request = {
                "pattern": "*",
                "limit": 10
            }
            
            search_response = await client.post(
                f"{base_url}/cache/search",
                json=search_request,
                headers=headers
            )
            
            if search_response.status_code == 200:
                search_result = search_response.json()
                print(f"✅ Cache search completed")
                print(f"   Found: {search_result['total_found']} keys")
                print(f"   Keys: {search_result['keys'][:5]}...")  # Show first 5
            else:
                print(f"❌ Cache search failed: {search_response.status_code}")
            
            # 8. Test user cache statistics
            print("\n8. 📊 Testing cache statistics...")
            stats_response = await client.get(
                f"{base_url}/cache/stats",
                headers=headers
            )
            
            if stats_response.status_code == 200:
                stats_data = stats_response.json()
                print(f"✅ Cache statistics retrieved")
                print(f"   Total keys: {stats_data['total_keys']}")
                print(f"   Total size: {stats_data.get('total_size_bytes', 'N/A')} bytes")
                print(f"   Categories: {stats_data.get('key_categories', {})}")
            else:
                print(f"❌ Cache statistics failed: {stats_response.status_code}")
            
            # 9. Test cache invalidation
            print("\n9. 🗑️  Testing cache invalidation...")
            invalidation_request = {
                "patterns": ["temp_*", "session_*"],
                "confirm": True
            }
            
            invalidation_response = await client.post(
                f"{base_url}/cache/invalidate",
                json=invalidation_request,
                headers=headers
            )
            
            if invalidation_response.status_code == 200:
                invalidation_result = invalidation_response.json()
                print(f"✅ Cache invalidation completed")
                print(f"   Invalidated: {invalidation_result['invalidated_count']} keys")
            else:
                print(f"❌ Cache invalidation failed: {invalidation_response.status_code}")
            
            # 10. Test cache deletion
            print("\n10. 🗑️ Testing cache deletion...")
            delete_response = await client.delete(
                f"{base_url}/cache/user_preferences",
                headers=headers
            )
            
            if delete_response.status_code == 200:
                print("✅ Cache item deleted successfully")
            else:
                print(f"❌ Cache deletion failed: {delete_response.status_code}")
            
            print("\n" + "=" * 50)
            print("🎉 Cache API integration test completed!")
            return True
            
        except Exception as e:
            print(f"\n❌ Test failed with exception: {e}")
            return False


async def test_direct_redis_cache():
    """Test direct Redis cache operations."""
    print("\n🔧 Testing Direct Redis Cache Operations")
    print("=" * 50)
    
    try:
        # Test basic cache operations
        await cache.set("test:direct", {"message": "Hello Redis!"}, 300)
        print("✅ Direct cache set successful")
        
        cached_value = await cache.get("test:direct")
        if cached_value:
            print(f"✅ Direct cache get successful: {cached_value}")
        else:
            print("❌ Direct cache get failed")
        
        # Test TTL
        ttl = await cache.ttl("test:direct")
        print(f"✅ Cache TTL: {ttl} seconds")
        
        # Test key existence
        exists = await cache.exists("test:direct")
        print(f"✅ Cache key exists: {exists}")
        
        # Test pattern operations
        await cache.set("test:pattern:1", "value1", 300)
        await cache.set("test:pattern:2", "value2", 300)
        
        keys = await cache.keys("test:pattern:*")
        print(f"✅ Pattern search found {len(keys)} keys: {keys}")
        
        # Test pattern deletion
        deleted_count = await cache.delete_pattern("test:*")
        print(f"✅ Pattern deletion removed {deleted_count} keys")
        
        # Test health check
        health = await cache.health_check()
        print(f"✅ Cache health: {health}")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct cache test failed: {e}")
        return False


async def main():
    """Run all cache integration tests."""
    print("🚀 Starting Cache Integration Tests")
    print("=" * 60)
    
    # Test direct Redis operations first
    redis_success = await test_direct_redis_cache()
    
    # Test API integration
    api_success = await test_cache_api_integration()
    
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print(f"   Direct Redis Cache: {'✅ PASS' if redis_success else '❌ FAIL'}")
    print(f"   Cache API Integration: {'✅ PASS' if api_success else '❌ FAIL'}")
    
    if redis_success and api_success:
        print("\n🎉 All tests passed! Cache integration is working correctly.")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the output above.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
