// API client with authentication and error handling
import axios, { AxiosInstance, AxiosError, InternalAxiosRequestConfig } from "axios";
import { config, apiEndpoints } from "./config";
import type { AuthTokens, ApiError } from "@/types";

// Extend AxiosRequestConfig to include _retry flag
interface ExtendedAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean;
}

// Create axios instance
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: config.API_BASE_URL,
    timeout: 10000,
    headers: {
      "Content-Type": "application/json",
    },
    withCredentials: true, // Include cookies for session auth
  });

  // Request interceptor to add auth token
  client.interceptors.request.use(
    (requestConfig) => {
      // Try to get token from localStorage
      if (typeof window !== "undefined") {
        const tokens = localStorage.getItem(config.TOKEN_STORAGE_KEY);
        if (tokens) {
          try {
            const parsedTokens: AuthTokens = JSON.parse(tokens);
            requestConfig.headers.Authorization = `Bearer ${parsedTokens.access_token}`;
          } catch (error) {
            console.warn("Failed to parse stored tokens:", error);
            localStorage.removeItem(config.TOKEN_STORAGE_KEY);
          }
        }
      }
      return requestConfig;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor for error handling and token refresh
  client.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      const originalRequest = error.config as ExtendedAxiosRequestConfig;

      // Handle 401 errors (token expired)
      if (error.response?.status === 401 && originalRequest && !originalRequest._retry) {
        originalRequest._retry = true;

        try {
          // Try to refresh token
          const tokens = localStorage.getItem(config.TOKEN_STORAGE_KEY);
          if (tokens) {
            const parsedTokens: AuthTokens = JSON.parse(tokens);
            const refreshResponse = await axios.post(
              apiEndpoints.auth.refresh,
              { refresh_token: parsedTokens.refresh_token },
              { withCredentials: true }
            );

            const newTokens: AuthTokens = refreshResponse.data;
            localStorage.setItem(config.TOKEN_STORAGE_KEY, JSON.stringify(newTokens));

            // Retry original request with new token
            originalRequest.headers.Authorization = `Bearer ${newTokens.access_token}`;
            return client(originalRequest);
          }
        } catch (refreshError) {
          // Refresh failed, redirect to login
          if (typeof window !== "undefined") {
            localStorage.removeItem(config.TOKEN_STORAGE_KEY);
            window.location.href = "/login";
          }
        }
      }

      // Transform error for consistent handling
      const apiError: ApiError = {
        detail: (error.response?.data as any)?.detail || error.message || "An error occurred",
        status_code: error.response?.status || 500,
      };

      return Promise.reject(apiError);
    }
  );

  return client;
};

// Export singleton instance
export const apiClient = createApiClient();

// Utility functions for common API operations
export const apiUtils = {
  // Store auth tokens
  storeTokens: (tokens: AuthTokens) => {
    if (typeof window !== "undefined") {
      localStorage.setItem(config.TOKEN_STORAGE_KEY, JSON.stringify(tokens));
    }
  },

  // Get stored tokens
  getStoredTokens: (): AuthTokens | null => {
    if (typeof window !== "undefined") {
      const tokens = localStorage.getItem(config.TOKEN_STORAGE_KEY);
      if (tokens) {
        try {
          return JSON.parse(tokens);
        } catch {
          localStorage.removeItem(config.TOKEN_STORAGE_KEY);
        }
      }
    }
    return null;
  },

  // Clear stored tokens
  clearTokens: () => {
    if (typeof window !== "undefined") {
      localStorage.removeItem(config.TOKEN_STORAGE_KEY);
    }
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    return apiUtils.getStoredTokens() !== null;
  },
};
