"""Test security and permissions."""
import pytest
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import (
    create_access_token,
    create_refresh_token,
    verify_token,
    get_password_hash,
    verify_password,
    generate_session_token,
    generate_verification_token
)
from app.core.permissions import PermissionManager
from app.core.constants import UserRole, Permission
from app.models.user import User
from app.crud.user import user_crud
from app.schemas.user import UserCreate


class TestPasswordHashing:
    """Test password hashing and verification."""
    
    def test_hash_password(self):
        """Test password hashing."""
        password = "testpassword123"
        hashed = get_password_hash(password)
        
        assert hashed != password
        assert len(hashed) > 0
        assert hashed.startswith("$2b$")
    
    def test_verify_password_correct(self):
        """Test password verification with correct password."""
        password = "testpassword123"
        hashed = get_password_hash(password)
        
        assert verify_password(password, hashed) is True
    
    def test_verify_password_incorrect(self):
        """Test password verification with incorrect password."""
        password = "testpassword123"
        wrong_password = "wrongpassword"
        hashed = get_password_hash(password)
        
        assert verify_password(wrong_password, hashed) is False
    
    def test_hash_different_passwords_different_hashes(self):
        """Test that different passwords produce different hashes."""
        password1 = "password1"
        password2 = "password2"
        
        hash1 = get_password_hash(password1)
        hash2 = get_password_hash(password2)
        
        assert hash1 != hash2
    
    def test_hash_same_password_different_hashes(self):
        """Test that same password produces different hashes (salt)."""
        password = "testpassword123"
        
        hash1 = get_password_hash(password)
        hash2 = get_password_hash(password)
        
        assert hash1 != hash2
        assert verify_password(password, hash1) is True
        assert verify_password(password, hash2) is True


class TestJWTTokens:
    """Test JWT token creation and verification."""
    
    def test_create_access_token(self):
        """Test access token creation."""
        data = {"sub": "user123"}
        token = create_access_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 0
        assert "." in token  # JWT format
    
    def test_create_refresh_token(self):
        """Test refresh token creation."""
        data = {"sub": "user123"}
        token = create_refresh_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 0
        assert "." in token  # JWT format
    
    def test_verify_valid_access_token(self):
        """Test verifying valid access token."""
        data = {"sub": "user123"}
        token = create_access_token(data)
        
        payload = verify_token(token, "access")
        
        assert payload is not None
        assert payload["sub"] == "user123"
        assert "exp" in payload
    
    def test_verify_valid_refresh_token(self):
        """Test verifying valid refresh token."""
        data = {"sub": "user123"}
        token = create_refresh_token(data)
        
        payload = verify_token(token, "refresh")
        
        assert payload is not None
        assert payload["sub"] == "user123"
        assert "exp" in payload
    
    def test_verify_invalid_token(self):
        """Test verifying invalid token."""
        invalid_token = "invalid.token.here"
        
        payload = verify_token(invalid_token, "access")
        
        assert payload is None
    
    def test_verify_expired_token(self):
        """Test verifying expired token."""
        data = {"sub": "user123"}
        # Create token that expires immediately
        token = create_access_token(data, expires_delta=timedelta(seconds=-1))
        
        payload = verify_token(token, "access")
        
        assert payload is None
    
    def test_verify_wrong_token_type(self):
        """Test verifying token with wrong type."""
        data = {"sub": "user123"}
        access_token = create_access_token(data)
        
        # Try to verify access token as refresh token
        payload = verify_token(access_token, "refresh")
        
        assert payload is None


class TestTokenGeneration:
    """Test token generation utilities."""
    
    def test_generate_session_token(self):
        """Test session token generation."""
        token = generate_session_token()

        assert isinstance(token, str)
        assert len(token) > 60  # URL-safe base64 encoded, approximately 86 chars for 64 bytes

    def test_generate_verification_token(self):
        """Test verification token generation."""
        token = generate_verification_token()

        assert isinstance(token, str)
        assert len(token) > 40  # URL-safe base64 encoded, approximately 43 chars for 32 bytes
    
    def test_tokens_are_unique(self):
        """Test that generated tokens are unique."""
        tokens = [generate_session_token() for _ in range(10)]
        
        assert len(set(tokens)) == 10  # All unique


class TestPermissionManager:
    """Test permission management system."""
    
    @pytest.mark.asyncio
    async def test_user_permissions(self, async_db: AsyncSession):
        """Test user role permissions."""
        # Create users with different roles
        user_data = UserCreate(
            email="<EMAIL>",
            password="password123",
            full_name="Regular User"
        )
        user = await user_crud.create_async(async_db, obj_in=user_data)
        user.role = UserRole.USER
        
        moderator_data = UserCreate(
            email="<EMAIL>",
            password="password123",
            full_name="Moderator User"
        )
        moderator = await user_crud.create_async(async_db, obj_in=moderator_data)
        moderator.role = UserRole.MODERATOR
        
        admin_data = UserCreate(
            email="<EMAIL>",
            password="password123",
            full_name="Admin User"
        )
        admin = await user_crud.create_async(async_db, obj_in=admin_data)
        admin.role = UserRole.ADMIN
        
        # Test user permissions
        user_perms = PermissionManager.get_user_permissions(user)
        assert Permission.READ in user_perms
        assert Permission.WRITE not in user_perms
        assert Permission.DELETE not in user_perms
        assert Permission.ADMIN not in user_perms
        
        # Test moderator permissions
        mod_perms = PermissionManager.get_user_permissions(moderator)
        assert Permission.READ in mod_perms
        assert Permission.WRITE in mod_perms
        assert Permission.DELETE not in mod_perms
        assert Permission.ADMIN not in mod_perms
        
        # Test admin permissions
        admin_perms = PermissionManager.get_user_permissions(admin)
        assert Permission.READ in admin_perms
        assert Permission.WRITE in admin_perms
        assert Permission.DELETE in admin_perms
        assert Permission.ADMIN in admin_perms
    
    @pytest.mark.asyncio
    async def test_superuser_permissions(self, async_db: AsyncSession):
        """Test superuser has all permissions."""
        user_data = UserCreate(
            email="<EMAIL>",
            password="password123",
            full_name="Super User"
        )
        user = await user_crud.create_async(async_db, obj_in=user_data)
        user.is_superuser = True
        
        # Superuser should have all permissions regardless of role
        assert PermissionManager.user_has_permission(user, Permission.READ)
        assert PermissionManager.user_has_permission(user, Permission.WRITE)
        assert PermissionManager.user_has_permission(user, Permission.DELETE)
        assert PermissionManager.user_has_permission(user, Permission.ADMIN)
    
    @pytest.mark.asyncio
    async def test_role_hierarchy(self, async_db: AsyncSession):
        """Test role hierarchy."""
        user_data = UserCreate(
            email="<EMAIL>",
            password="password123",
            full_name="Test User"
        )
        user = await user_crud.create_async(async_db, obj_in=user_data)
        
        # Test user role
        user.role = UserRole.USER
        assert PermissionManager.user_has_role(user, UserRole.USER)
        assert not PermissionManager.user_has_role(user, UserRole.MODERATOR)
        assert not PermissionManager.user_has_role(user, UserRole.ADMIN)
        
        # Test moderator role
        user.role = UserRole.MODERATOR
        assert PermissionManager.user_has_role(user, UserRole.USER)
        assert PermissionManager.user_has_role(user, UserRole.MODERATOR)
        assert not PermissionManager.user_has_role(user, UserRole.ADMIN)
        
        # Test admin role
        user.role = UserRole.ADMIN
        assert PermissionManager.user_has_role(user, UserRole.USER)
        assert PermissionManager.user_has_role(user, UserRole.MODERATOR)
        assert PermissionManager.user_has_role(user, UserRole.ADMIN)
    
    @pytest.mark.asyncio
    async def test_resource_access(self, async_db: AsyncSession):
        """Test resource access control."""
        user_data = UserCreate(
            email="<EMAIL>",
            password="password123",
            full_name="Resource Owner"
        )
        owner = await user_crud.create_async(async_db, obj_in=user_data)
        owner.role = UserRole.USER
        
        other_data = UserCreate(
            email="<EMAIL>",
            password="password123",
            full_name="Other User"
        )
        other = await user_crud.create_async(async_db, obj_in=other_data)
        other.role = UserRole.USER
        
        admin_data = UserCreate(
            email="<EMAIL>",
            password="password123",
            full_name="Admin User"
        )
        admin = await user_crud.create_async(async_db, obj_in=admin_data)
        admin.role = UserRole.ADMIN
        
        resource_owner_id = str(owner.id)
        
        # Owner can access their own resource
        assert PermissionManager.user_can_access_resource(
            owner, resource_owner_id, Permission.WRITE
        )
        
        # Other user cannot access without permission
        assert not PermissionManager.user_can_access_resource(
            other, resource_owner_id, Permission.WRITE
        )
        
        # Admin can access any resource
        assert PermissionManager.user_can_access_resource(
            admin, resource_owner_id, Permission.WRITE
        )
