"""Cache-related Pydantic schemas."""
from typing import Any, Optional, Dict, List
from pydantic import BaseModel, Field, validator
from datetime import datetime


class CacheItemRequest(BaseModel):
    """Request model for setting cache items."""
    data: Any = Field(..., description="Data to cache")
    ttl: Optional[int] = Field(
        default=300, 
        ge=1, 
        le=86400,  # Max 24 hours
        description="Time to live in seconds (1 second to 24 hours)"
    )


class CacheItemResponse(BaseModel):
    """Response model for cache items."""
    data: Any = Field(..., description="Cached data")
    ttl: Optional[int] = Field(None, description="Remaining TTL in seconds")
    created_at: Optional[datetime] = Field(None, description="Cache creation timestamp")


class CacheKeyInfo(BaseModel):
    """Information about a cache key."""
    key: str = Field(..., description="Cache key")
    exists: bool = Field(..., description="Whether the key exists")
    ttl: Optional[int] = Field(None, description="Remaining TTL in seconds")
    size: Optional[int] = Field(None, description="Size of cached data in bytes")


class CacheStatsResponse(BaseModel):
    """Cache statistics response."""
    total_keys: int = Field(..., description="Total number of cache keys")
    user_keys: int = Field(..., description="Number of user-specific keys")
    memory_usage: Optional[str] = Field(None, description="Memory usage information")
    hit_rate: Optional[float] = Field(None, description="Cache hit rate percentage")
    uptime: Optional[str] = Field(None, description="Redis uptime")


class CacheHealthResponse(BaseModel):
    """Cache health check response."""
    status: str = Field(..., description="Health status")
    response_time_ms: float = Field(..., description="Response time in milliseconds")
    connection: str = Field(..., description="Connection status")
    error: Optional[str] = Field(None, description="Error message if unhealthy")


class CacheBulkRequest(BaseModel):
    """Request model for bulk cache operations."""
    items: Dict[str, CacheItemRequest] = Field(
        ..., 
        description="Dictionary of key-value pairs to cache"
    )
    
    @validator('items')
    def validate_items_count(cls, v):
        if len(v) > 100:  # Limit bulk operations
            raise ValueError("Cannot process more than 100 items in bulk")
        return v


class CacheBulkResponse(BaseModel):
    """Response model for bulk cache operations."""
    success_count: int = Field(..., description="Number of successfully processed items")
    failed_count: int = Field(..., description="Number of failed items")
    failed_keys: List[str] = Field(default_factory=list, description="Keys that failed to process")
    errors: Dict[str, str] = Field(default_factory=dict, description="Error messages for failed keys")


class CacheSearchRequest(BaseModel):
    """Request model for searching cache keys."""
    pattern: str = Field(
        ..., 
        description="Search pattern (supports Redis glob patterns like user:* or *:preferences)"
    )
    limit: Optional[int] = Field(
        default=100, 
        ge=1, 
        le=1000,
        description="Maximum number of keys to return"
    )


class CacheSearchResponse(BaseModel):
    """Response model for cache key search."""
    keys: List[str] = Field(..., description="Matching cache keys")
    total_found: int = Field(..., description="Total number of matching keys")
    limited: bool = Field(..., description="Whether results were limited")


class CacheInvalidationRequest(BaseModel):
    """Request model for cache invalidation."""
    patterns: List[str] = Field(
        ..., 
        description="List of patterns to invalidate (supports glob patterns)"
    )
    confirm: bool = Field(
        default=False,
        description="Confirmation flag for destructive operations"
    )
    
    @validator('patterns')
    def validate_patterns(cls, v):
        if len(v) > 50:  # Limit number of patterns
            raise ValueError("Cannot process more than 50 patterns at once")
        return v


class CacheInvalidationResponse(BaseModel):
    """Response model for cache invalidation."""
    invalidated_count: int = Field(..., description="Number of keys invalidated")
    patterns_processed: List[str] = Field(..., description="Patterns that were processed")
    errors: List[str] = Field(default_factory=list, description="Any errors encountered")


class UserCacheStatsResponse(BaseModel):
    """User-specific cache statistics."""
    user_id: str = Field(..., description="User ID")
    total_keys: int = Field(..., description="Total cache keys for this user")
    total_size_bytes: Optional[int] = Field(None, description="Total size of user's cached data")
    oldest_key_age: Optional[int] = Field(None, description="Age of oldest cache entry in seconds")
    newest_key_age: Optional[int] = Field(None, description="Age of newest cache entry in seconds")
    key_categories: Dict[str, int] = Field(
        default_factory=dict, 
        description="Breakdown of keys by category (e.g., user_data, preferences, etc.)"
    )
