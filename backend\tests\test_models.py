"""Test database models."""
import pytest
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.models.user import User
from app.models.session import UserSession
from app.models.email_verification import EmailVerification
from app.crud.user import user_crud
from app.crud.session import session_crud
from app.crud.email_verification import email_verification_crud
from app.schemas.user import UserCreate
from app.core.constants import UserRole, Permission


class TestUserModel:
    """Test User model."""
    
    @pytest.mark.asyncio
    async def test_create_user(self, async_db: AsyncSession):
        """Test creating a user."""
        user_data = UserCreate(
            email="<EMAIL>",
            password="password123",
            full_name="Test User"
        )
        
        user = await user_crud.create_async(async_db, obj_in=user_data)
        
        assert user.email == user_data.email
        assert user.full_name == user_data.full_name
        assert user.is_active is True
        assert user.is_verified is False
        assert user.role == UserRole.USER
        assert user.hashed_password != user_data.password
    
    @pytest.mark.asyncio
    async def test_user_permissions(self, async_db: AsyncSession):
        """Test user permission methods."""
        user_data = UserCreate(
            email="<EMAIL>",
            password="password123",
            full_name="Test User"
        )
        
        user = await user_crud.create_async(async_db, obj_in=user_data)
        
        # Regular user permissions
        assert user.has_permission(Permission.READ) is True
        assert user.has_permission(Permission.WRITE) is False
        assert user.has_permission(Permission.DELETE) is False
        assert user.has_permission(Permission.ADMIN) is False
        
        # Admin user permissions
        user.role = UserRole.ADMIN
        assert user.has_permission(Permission.READ) is True
        assert user.has_permission(Permission.WRITE) is True
        assert user.has_permission(Permission.DELETE) is True
        assert user.has_permission(Permission.ADMIN) is True
        
        # Superuser permissions
        user.role = UserRole.USER
        user.is_superuser = True
        assert user.has_permission(Permission.ADMIN) is True
    
    @pytest.mark.asyncio
    async def test_user_admin_check(self, async_db: AsyncSession):
        """Test user admin check."""
        user_data = UserCreate(
            email="<EMAIL>",
            password="password123",
            full_name="Test User"
        )
        
        user = await user_crud.create_async(async_db, obj_in=user_data)
        
        # Regular user
        assert user.is_admin() is False
        
        # Admin user
        user.role = UserRole.ADMIN
        assert user.is_admin() is True
        
        # Superuser
        user.role = UserRole.USER
        user.is_superuser = True
        assert user.is_admin() is True


class TestUserSessionModel:
    """Test UserSession model."""
    
    @pytest.mark.asyncio
    async def test_create_session(self, async_db: AsyncSession, test_user: User):
        """Test creating a user session."""
        session = await session_crud.create_session(
            async_db,
            user_id=str(test_user.id),
            refresh_token="test_refresh_token",
            user_agent="Test Browser",
            ip_address="127.0.0.1"
        )
        
        assert session.user_id == test_user.id
        assert session.user_agent == "Test Browser"
        assert session.ip_address == "127.0.0.1"
        assert session.is_active is True
        assert session.expires_at > datetime.utcnow()
    
    @pytest.mark.asyncio
    async def test_session_expiration(self, async_db: AsyncSession, test_user: User):
        """Test session expiration check."""
        # Create expired session
        session = await session_crud.create_session(
            async_db,
            user_id=str(test_user.id),
            refresh_token="test_refresh_token",
            user_agent="Test Browser",
            ip_address="127.0.0.1"
        )
        
        # Manually set expiration to past
        session.expires_at = datetime.utcnow() - timedelta(hours=1)
        await async_db.commit()
        
        assert session.is_expired() is True
        assert session.is_valid() is False
    
    @pytest.mark.asyncio
    async def test_session_deactivation(self, async_db: AsyncSession, test_user: User):
        """Test session deactivation."""
        session = await session_crud.create_session(
            async_db,
            user_id=str(test_user.id),
            refresh_token="test_refresh_token",
            user_agent="Test Browser",
            ip_address="127.0.0.1"
        )
        
        # Deactivate session
        await session_crud.deactivate_session(async_db, session_token=session.session_token)
        await async_db.refresh(session)
        
        assert session.is_active is False
        assert session.is_valid() is False


class TestEmailVerificationModel:
    """Test EmailVerification model."""
    
    @pytest.mark.asyncio
    async def test_create_verification(self, async_db: AsyncSession, test_user: User):
        """Test creating email verification."""
        verification = await email_verification_crud.create_verification(
            async_db,
            user_id=str(test_user.id),
            email=test_user.email
        )
        
        assert verification.user_id == test_user.id
        assert verification.email == test_user.email
        assert verification.is_used is False
        assert verification.expires_at > datetime.utcnow()
        assert len(verification.token) == 43  # URL-safe base64 encoding of 32 bytes
    
    @pytest.mark.asyncio
    async def test_verification_expiration(self, async_db: AsyncSession, test_user: User):
        """Test verification token expiration."""
        verification = await email_verification_crud.create_verification(
            async_db,
            user_id=str(test_user.id),
            email=test_user.email
        )
        
        # Manually set expiration to past
        verification.expires_at = datetime.utcnow() - timedelta(hours=1)
        await async_db.commit()
        
        assert verification.is_valid() is False
    
    @pytest.mark.asyncio
    async def test_verification_usage(self, async_db: AsyncSession, test_user: User):
        """Test verification token usage."""
        verification = await email_verification_crud.create_verification(
            async_db,
            user_id=str(test_user.id),
            email=test_user.email
        )
        
        # Mark as used
        verification.mark_as_used()
        await async_db.commit()
        
        assert verification.is_used is True
        assert verification.verified_at is not None
        assert verification.is_valid() is False
    
    @pytest.mark.asyncio
    async def test_verify_token(self, async_db: AsyncSession, test_user: User):
        """Test token verification."""
        verification = await email_verification_crud.create_verification(
            async_db,
            user_id=str(test_user.id),
            email=test_user.email
        )
        
        # Valid token
        result = await email_verification_crud.get_by_token(async_db, token=verification.token)
        assert result is not None
        assert result.user_id == test_user.id

        # Invalid token
        result = await email_verification_crud.get_by_token(async_db, token="invalid_token")
        assert result is None

        # Used token
        verification.mark_as_used()
        await async_db.commit()

        result = await email_verification_crud.get_by_token(async_db, token=verification.token)
        assert result is not None
        assert result.is_valid() is False  # Should be invalid because it's used


class TestModelRelationships:
    """Test model relationships."""
    
    @pytest.mark.asyncio
    async def test_user_sessions_relationship(self, async_db: AsyncSession, test_user: User):
        """Test user-sessions relationship."""
        # Create multiple sessions
        session1 = await session_crud.create_session(
            async_db,
            user_id=str(test_user.id),
            refresh_token="test_refresh_token_1",
            user_agent="Browser 1",
            ip_address="127.0.0.1"
        )

        session2 = await session_crud.create_session(
            async_db,
            user_id=str(test_user.id),
            refresh_token="test_refresh_token_2",
            user_agent="Browser 2",
            ip_address="*********"
        )
        
        await async_db.refresh(test_user)

        # Check relationship by querying sessions
        result = await async_db.execute(
            select(UserSession).where(UserSession.user_id == test_user.id)
        )
        sessions = result.scalars().all()
        assert len(sessions) == 2
        session_tokens = [s.session_token for s in sessions]
        assert session1.session_token in session_tokens
        assert session2.session_token in session_tokens
    
    @pytest.mark.asyncio
    async def test_user_email_verifications_relationship(self, async_db: AsyncSession, test_user: User):
        """Test user-email_verifications relationship."""
        # Create multiple verifications
        verification1 = await email_verification_crud.create_verification(
            async_db,
            user_id=str(test_user.id),
            email=test_user.email
        )
        
        verification2 = await email_verification_crud.create_verification(
            async_db,
            user_id=str(test_user.id),
            email="<EMAIL>"
        )
        
        await async_db.refresh(test_user)

        # Check relationship by querying email verifications
        result = await async_db.execute(
            select(EmailVerification).where(EmailVerification.user_id == test_user.id)
        )
        verifications = result.scalars().all()
        assert len(verifications) == 2
        verification_tokens = [v.token for v in verifications]
        assert verification1.token in verification_tokens
        assert verification2.token in verification_tokens
