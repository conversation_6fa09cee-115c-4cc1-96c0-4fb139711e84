"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient, apiUtils } from "@/lib/api-client";
import { apiEndpoints } from "@/lib/config";
import type { User, AuthTokens } from "@/types";

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (tokens: AuthTokens) => void;
  logout: () => void;
  refetchUser: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const queryClient = useQueryClient();

  // Check authentication status on mount
  useEffect(() => {
    const tokens = apiUtils.getStoredTokens();
    setIsAuthenticated(!!tokens);
  }, []);

  // Fetch user data when authenticated
  const {
    data: user,
    isLoading,
    refetch: refetchUser,
  } = useQuery({
    queryKey: ["auth", "user"],
    queryFn: async (): Promise<User> => {
      const response = await apiClient.get(apiEndpoints.auth.me);
      return response.data;
    },
    enabled: isAuthenticated,
    retry: (failureCount, error: any) => {
      // Don't retry on 401 errors
      if (error?.status_code === 401) {
        return false;
      }
      return failureCount < 3;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const login = (tokens: AuthTokens) => {
    apiUtils.storeTokens(tokens);
    setIsAuthenticated(true);
    // Invalidate and refetch user data
    queryClient.invalidateQueries({ queryKey: ["auth"] });
  };

  const logout = async () => {
    try {
      // Call logout endpoint to invalidate session on server
      await apiClient.post(apiEndpoints.auth.logout);
    } catch (error) {
      // Continue with logout even if server call fails
      console.warn("Logout API call failed:", error);
    } finally {
      // Clear local storage and state
      apiUtils.clearTokens();
      setIsAuthenticated(false);
      
      // Clear all cached data
      queryClient.clear();
      
      // Redirect to login page
      window.location.href = "/login";
    }
  };

  const value: AuthContextType = {
    user: user || null,
    isLoading: isLoading && isAuthenticated,
    isAuthenticated,
    login,
    logout,
    refetchUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
