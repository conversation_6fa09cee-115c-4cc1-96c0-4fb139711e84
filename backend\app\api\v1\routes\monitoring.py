"""Monitoring and health check endpoints."""
import asyncio
import psutil
import platform
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.api.deps import get_async_db
from app.core.config import settings
from app.core.cache import cache, CacheManager
from app.core.performance import get_performance_stats, performance_health_check
from app.core.versioning import version_manager
from app.schemas.response import HealthResponse, PerformanceResponse, APIResponse
from app.core.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/monitoring",
    tags=["Monitoring"],
    responses={
        500: {"description": "Internal server error"},
    }
)


@router.get(
    "/health",
    response_model=HealthResponse,
    summary="Health Check",
    description="Get application health status with detailed checks"
)
async def health_check(db: AsyncSession = Depends(get_async_db)):
    """Comprehensive health check endpoint."""
    start_time = time.time()
    checks = {}
    overall_status = "healthy"
    
    try:
        # Database health check
        try:
            await db.execute(text("SELECT 1"))
            checks["database"] = {
                "status": "healthy",
                "response_time_ms": round((time.time() - start_time) * 1000, 2)
            }
        except Exception as e:
            checks["database"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            overall_status = "unhealthy"
        
        # Cache health check
        cache_health = await CacheManager.health_check()
        checks["cache"] = cache_health
        if cache_health["status"] != "healthy":
            overall_status = "warning" if overall_status == "healthy" else "unhealthy"
        
        # Memory check
        memory = psutil.virtual_memory()
        memory_usage_percent = memory.percent
        checks["memory"] = {
            "status": "healthy" if memory_usage_percent < 80 else "warning" if memory_usage_percent < 90 else "unhealthy",
            "usage_percent": memory_usage_percent,
            "available_gb": round(memory.available / (1024**3), 2),
            "total_gb": round(memory.total / (1024**3), 2)
        }
        
        if memory_usage_percent > 90:
            overall_status = "unhealthy"
        elif memory_usage_percent > 80 and overall_status == "healthy":
            overall_status = "warning"
        
        # CPU check
        cpu_percent = psutil.cpu_percent(interval=1)
        checks["cpu"] = {
            "status": "healthy" if cpu_percent < 80 else "warning" if cpu_percent < 90 else "unhealthy",
            "usage_percent": cpu_percent,
            "core_count": psutil.cpu_count()
        }
        
        if cpu_percent > 90:
            overall_status = "unhealthy"
        elif cpu_percent > 80 and overall_status == "healthy":
            overall_status = "warning"
        
        # Disk check
        disk = psutil.disk_usage('/')
        disk_usage_percent = (disk.used / disk.total) * 100
        checks["disk"] = {
            "status": "healthy" if disk_usage_percent < 80 else "warning" if disk_usage_percent < 90 else "unhealthy",
            "usage_percent": round(disk_usage_percent, 2),
            "free_gb": round(disk.free / (1024**3), 2),
            "total_gb": round(disk.total / (1024**3), 2)
        }
        
        if disk_usage_percent > 90:
            overall_status = "unhealthy"
        elif disk_usage_percent > 80 and overall_status == "healthy":
            overall_status = "warning"
        
        # API version check
        checks["api_version"] = {
            "status": "healthy",
            "current_version": str(version_manager.default_version),
            "supported_versions": version_manager.get_version_info()["supported_versions"]
        }
        
        total_time = round((time.time() - start_time) * 1000, 2)
        
        return HealthResponse(
            status=overall_status,
            version=settings.VERSION,
            environment=settings.ENVIRONMENT,
            timestamp=datetime.utcnow().isoformat(),
            checks={
                **checks,
                "response_time_ms": total_time
            }
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="unhealthy",
            version=settings.VERSION,
            environment=settings.ENVIRONMENT,
            timestamp=datetime.utcnow().isoformat(),
            checks={"error": str(e)}
        )


@router.get(
    "/health/simple",
    summary="Simple Health Check",
    description="Simple health check that returns 200 OK if service is running"
)
async def simple_health_check():
    """Simple health check for load balancers."""
    return {"status": "ok", "timestamp": datetime.utcnow().isoformat()}


@router.get(
    "/performance",
    response_model=PerformanceResponse,
    summary="Performance Statistics",
    description="Get detailed performance statistics and metrics"
)
async def performance_stats():
    """Get performance statistics."""
    try:
        health_check_result = await performance_health_check()

        return PerformanceResponse(
            status=health_check_result["status"],
            issues=health_check_result["issues"],
            stats=health_check_result["stats"]
        )

    except Exception as e:
        logger.error(f"Performance stats failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve performance statistics"
        )


@router.get(
    "/metrics",
    summary="Application Metrics",
    description="Get detailed application metrics in Prometheus format"
)
async def get_metrics():
    """Get application metrics in Prometheus format."""
    try:
        # Basic system metrics
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent()
        disk = psutil.disk_usage('/')
        
        # Cache metrics
        cache_stats = await cache.get_stats()

        # Format as Prometheus metrics
        metrics = [
            f"# HELP system_memory_usage_percent System memory usage percentage",
            f"# TYPE system_memory_usage_percent gauge",
            f"system_memory_usage_percent {memory.percent}",
            "",
            f"# HELP system_cpu_usage_percent System CPU usage percentage",
            f"# TYPE system_cpu_usage_percent gauge",
            f"system_cpu_usage_percent {cpu_percent}",
            "",
            f"# HELP system_disk_usage_percent System disk usage percentage",
            f"# TYPE system_disk_usage_percent gauge",
            f"system_disk_usage_percent {(disk.used / disk.total) * 100}",
            "",
        ]

        # Add Redis cache metrics if available
        if cache_stats:
            if "connected_clients" in cache_stats:
                metrics.extend([
                    f"# HELP redis_connected_clients Number of connected Redis clients",
                    f"# TYPE redis_connected_clients gauge",
                    f"redis_connected_clients {cache_stats['connected_clients']}",
                    "",
                ])

            if "used_memory" in cache_stats:
                metrics.extend([
                    f"# HELP redis_used_memory Redis memory usage in bytes",
                    f"# TYPE redis_used_memory gauge",
                    f"redis_used_memory {cache_stats['used_memory']}",
                    "",
                ])

            if "keyspace_hits" in cache_stats:
                metrics.extend([
                    f"# HELP redis_keyspace_hits Redis keyspace hits",
                    f"# TYPE redis_keyspace_hits counter",
                    f"redis_keyspace_hits {cache_stats['keyspace_hits']}",
                    "",
                ])

            if "keyspace_misses" in cache_stats:
                metrics.extend([
                    f"# HELP redis_keyspace_misses Redis keyspace misses",
                    f"# TYPE redis_keyspace_misses counter",
                    f"redis_keyspace_misses {cache_stats['keyspace_misses']}",
                    "",
                ])
        
        return "\n".join(metrics)
        
    except Exception as e:
        logger.error(f"Metrics endpoint failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve metrics"
        )


@router.get(
    "/info",
    summary="Application Information",
    description="Get comprehensive application information"
)
async def app_info():
    """Get application information."""
    try:
        return APIResponse.success({
            "application": {
                "name": settings.PROJECT_NAME,
                "version": settings.VERSION,
                "description": settings.DESCRIPTION,
                "environment": settings.ENVIRONMENT
            },
            "api": {
                "version_info": version_manager.get_version_info(),
                "base_url": settings.API_V1_STR
            },
            "system": {
                "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                "platform": platform.platform(),
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2)
            },
            "features": {
                "caching_enabled": settings.ENABLE_CACHING,
                "rate_limiting_enabled": True,
                "cors_enabled": len(settings.BACKEND_CORS_ORIGINS) > 0,
                "email_enabled": settings.RESEND_API_KEY is not None
            },
            "timestamp": datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"App info endpoint failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve application information"
        )


@router.post(
    "/cache/clear",
    summary="Clear Cache",
    description="Clear application cache (admin only)"
)
async def clear_cache():
    """Clear application cache."""
    try:
        await cache.clear_all()
        logger.info("Cache cleared via monitoring endpoint")
        
        return APIResponse.success(
            message="Cache cleared successfully"
        )
        
    except Exception as e:
        logger.error(f"Cache clear failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear cache"
        )


@router.get(
    "/cache/stats",
    summary="Cache Statistics",
    description="Get detailed cache statistics"
)
async def cache_stats():
    """Get cache statistics."""
    try:
        stats = await cache.get_stats()
        health = await CacheManager.health_check()
        
        return APIResponse.success({
            "statistics": stats,
            "health": health,
            "timestamp": datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Cache stats failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve cache statistics"
        )
