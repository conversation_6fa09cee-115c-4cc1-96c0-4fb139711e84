#!/usr/bin/env node

// <PERSON>ript to demonstrate OpenAPI type checking mechanism
const { execSync } = require('child_process');
const path = require('path');

console.log('🔍 OpenAPI Type Checking Demonstration');
console.log('=====================================\n');

// 1. Check if backend is running
console.log('1. Checking backend availability...');
try {
  const response = execSync('curl -s http://localhost:8000/health', { encoding: 'utf8' });
  console.log('✅ Backend is running\n');
} catch (error) {
  console.log('❌ Backend is not running. Please start the backend first.');
  console.log('   Run: cd backend && uv run uvicorn app.main:app --reload\n');
  process.exit(1);
}

// 2. Generate fresh types from OpenAPI schema
console.log('2. Generating TypeScript types from OpenAPI schema...');
try {
  execSync('npm run generate:types', { stdio: 'inherit', cwd: process.cwd() });
  console.log('✅ Types generated successfully\n');
} catch (error) {
  console.log('❌ Failed to generate types');
  process.exit(1);
}

// 3. Run type validation tests
console.log('3. Running type validation tests...');
try {
  execSync('npm test -- type-validation.test.ts --verbose', { stdio: 'inherit', cwd: process.cwd() });
  console.log('✅ All type validation tests passed\n');
} catch (error) {
  console.log('❌ Type validation tests failed');
  process.exit(1);
}

// 4. Check TypeScript compilation
console.log('4. Checking TypeScript compilation...');
try {
  execSync('npx tsc --noEmit', { stdio: 'inherit', cwd: process.cwd() });
  console.log('✅ TypeScript compilation successful\n');
} catch (error) {
  console.log('❌ TypeScript compilation failed');
  console.log('This indicates type safety issues between frontend and backend\n');
  process.exit(1);
}

// 5. Summary
console.log('📋 Type Safety Summary');
console.log('=====================');
console.log('✅ Backend OpenAPI schema is accessible');
console.log('✅ TypeScript types generated from OpenAPI schema');
console.log('✅ Runtime type validation is working');
console.log('✅ Compile-time type checking is working');
console.log('✅ Frontend and backend types are synchronized\n');

console.log('🎯 Key Benefits:');
console.log('• Automatic type generation from backend schema');
console.log('• Compile-time error detection for API mismatches');
console.log('• Runtime validation for API responses');
console.log('• Type-safe API client with full IntelliSense');
console.log('• Automatic detection of schema changes\n');

console.log('🔧 Usage:');
console.log('• Run "npm run generate:types" to update types');
console.log('• Run "npm run generate:types:check" to verify types are up-to-date');
console.log('• Import types from "@/types/api" for type-safe API calls');
console.log('• Use TypedApiClient for fully typed API interactions\n');

console.log('✨ Type checking mechanism is working properly!');
