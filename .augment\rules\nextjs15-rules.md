---
type: "manual"
---

# Next.js 15+ Frontend Development Rules & Best Practices

This document outlines the standards for developing robust, maintainable, and production-ready Next.js 15+ frontends, optimized for seamless integration with FastAPI backends. These rules are refined based on successfully tested implementation patterns from our current FastAPI backend.

---

## 1. Code Style & General Practices

- **DRY & KISS:**
  Always reuse components, hooks, and utility functions. Keep code concise and easy to follow.

- **Type Safety:**
  Use TypeScript for all code. Strictly type props, state, context, and API responses.
  - Never use "any" type - use proper TypeScript types or unknown/object when needed
  - Component/functional scale types can stay within the same component
  - Global scale types shall be prepared and managed under `types/` directory for type safety and reusability
  - Component files: maximum 350 lines
  - Server actions or controlling logic flow: maximum 300 lines to maintain good code review

- **ESLint & Prettier:**
  Configure and enforce linting rules. Use Prettier for consistent formatting.
  Set up `eslint-config-next`, `@typescript-eslint/eslint-plugin`, and Prettier in CI.

- **Package Management:**
  Always use `npm`, `yarn`, or `pnpm` for dependency management. Never manually edit `package.json` dependencies - use package manager commands to ensure proper version resolution and lock file updates.

- **Folder Structure:**
  Use a modular structure optimized for FastAPI integration:

```
frontend/
├── src/
│   ├── app/
│   │   ├── (auth)/
│   │   │   ├── login/
│   │   │   │   ├── page.tsx
│   │   │   │   └── loading.tsx
│   │   │   ├── signup/
│   │   │   │   ├── page.tsx
│   │   │   │   └── loading.tsx
│   │   │   ├── verify-email/
│   │   │   │   └── page.tsx
│   │   │   └── layout.tsx
│   │   ├── (dashboard)/
│   │   │   ├── page.tsx
│   │   │   ├── layout.tsx
│   │   │   └── loading.tsx
│   │   ├── api/
│   │   │   └── auth/
│   │   │       └── route.ts
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   ├── not-found.tsx
│   │   └── middleware.ts
│   ├── components/
│   │   ├── ui/
│   │   │   ├── button.tsx
│   │   │   ├── card.tsx
│   │   │   ├── input.tsx
│   │   │   ├── form.tsx
│   │   │   └── toast.tsx
│   │   ├── layout/
│   │   │   ├── header.tsx
│   │   │   ├── footer.tsx
│   │   │   └── sidebar.tsx
│   │   ├── auth/
│   │   │   ├── login-form.tsx
│   │   │   ├── signup-form.tsx
│   │   │   └── auth-guard.tsx
│   │   └── shared/
│   │       ├── navbar.tsx
│   │       ├── theme-toggle.tsx
│   │       └── loading-spinner.tsx
│   ├── lib/
│   │   ├── utils.ts
│   │   ├── api.ts
│   │   ├── auth.ts
│   │   ├── validations.ts
│   │   └── constants.ts
│   ├── hooks/
│   │   ├── use-toast.ts
│   │   ├── use-auth.ts
│   │   ├── use-api.ts
│   │   └── use-local-storage.ts
│   ├── types/
│   │   ├── index.ts
│   │   ├── auth.ts
│   │   ├── api.ts
│   │   └── user.ts
│   ├── actions/
│   │   ├── auth.ts
│   │   └── user.ts
│   ├── context/
│   │   ├── auth-context.tsx   
│   │   └── theme-context.tsx
│   └── store/
│       └── auth-store.ts
├── public/
│   ├── images/
│   └── favicon.ico
├── __tests__/
│   ├── components/
│   ├── pages/
│   ├── hooks/
│   └── e2e/
├── components.json
├── next.config.mjs
├── tsconfig.json
├── tailwind.config.ts
├── jest.config.js
├── playwright.config.ts
└── package.json
```

- **Naming Conventions:**
  Use PascalCase for components, camelCase for functions/variables, SCREAMING_SNAKE_CASE for constants.

- **Documentation:**
  Document components, hooks, and utils with JSDoc or TSDoc.
  Maintain a `/docs` folder for architecture notes and onboarding.

---

## 2. Data Fetching & API Integration

- **Primary Data Fetching Strategy - TanStack Query (React Query):**
  - **Strengths:** Handles caching, re-fetching, background updates, and synchronization out-of-the-box
  - **Benefits:** Simplifies loading/error/UI state management, great for highly interactive UIs and real-time updates
  - **Features:** Built-in support for optimistic updates, pagination, query invalidation, and seamless React lifecycle integration
  - **Use Case:** Ideal for complex client-side state management, apps with many API calls, or when you need advanced features (caching, polling, stale-while-revalidate)
  - **Implementation:** Use `fetch` or `axios` inside your React Query functions for custom request logic

- **Form Data Validation:**
  - Use **Zod** for schema validation with **React Hook Form** resolver for frontend form data validation
  - Create reusable validation schemas in `/lib/validations.ts`
  - Ensure client-side validation matches FastAPI backend Pydantic models

- **React Server Components (RSC):**
  - Leverage RSC for static/dynamic rendering and optimal performance
  - Use for initial data loading and SEO-critical content
  - Combine with client components for interactive features

- **API Communication:**
  - **Centralized API Client:** Create typed API client in `/lib/api.ts` with proper error handling
  - **OpenAPI Integration:** Generate TypeScript types from FastAPI's OpenAPI spec using `openapi-typescript`
  - **Base Configuration:** Set up axios/fetch with base URL, interceptors, and authentication headers
  - **Versioning:** Always use versioned API endpoints (`/api/v1/`, `/api/v2/`)

- **Error Handling:**
  - Always handle loading, error, and empty states in UI components
  - Implement global error boundary for unhandled errors
  - Use consistent error message formatting matching FastAPI responses

- **Environment Variables:**
  - Store API URLs and non-sensitive config in `.env.local`
  - Use `NEXT_PUBLIC_` prefix only for client-side variables
  - Never leak sensitive secrets to the client

---

## 3. Authentication & Security (Based on Current FastAPI Implementation)

- **Dual Authentication Strategy:**
  - **JWT Authentication:** For API calls and mobile/SPA clients
  - **Session Authentication:** For browser-based SSR/CSR with HTTPOnly cookies
  - **Automatic Fallback:** Frontend should try JWT first, then fall back to session auth

- **Authentication Flow Implementation:**
  - **Login:** Send credentials to `/api/v1/auth/login`, receive both JWT tokens and session cookie
  - **Token Refresh:** Use `/api/v1/auth/refresh` with refresh token for new access tokens
  - **Session Management:** Leverage HTTPOnly cookies for secure session storage
  - **Logout:** Call `/api/v1/auth/logout` to invalidate both JWT and session

- **Email Verification Integration:**
  - Implement email verification flow using `/api/v1/auth/verify-email`
  - Handle verification tokens from email links
  - Show appropriate UI states for unverified accounts

- **Security Best Practices:**
  - Store JWT tokens in memory or secure storage (never localStorage for sensitive data)
  - Use HTTPOnly cookies for session tokens (handled by FastAPI backend)
  - Implement CSRF protection for session-based requests
  - Handle rate limiting responses from backend (429 status codes)

- **Role-Based Access Control:**
  - Implement role-based UI rendering based on user permissions
  - Use route guards and middleware for protected pages
  - Conditionally render components based on user roles

- **Frontend Auth Context:**
  - Create centralized auth context with user state management
  - Provide hooks for authentication status, user data, and auth actions
  - Handle authentication state persistence across page reloads

---

## 4. State Management

- **Authentication State:**
  Use React Context or Zustand for global auth state management.

- **Local Component State:**
  Use React hooks (useState, useReducer) for component-specific state.

- **Server State:**
  Use TanStack Query for all server state management and caching.

- **Global Application State:**
  For complex flows, consider Zustand or Redux Toolkit, but prefer React Context for simpler cases.

- **Hydration:**
  Avoid unnecessary hydration; prefer server components for static data.

---

## 5. Styling

- **CSS Framework:**
  Use Tailwind CSS with shadcn/ui for scalable, maintainable styles.

- **Component Library:**
  Implement shadcn/ui components for consistent design system.

- **Theme Support:**
  Implement dark/light mode with proper accessibility features.

- **Global Styles:**
  Store global styles in `/app/globals.css` with CSS variables for theming.

---

## 6. Routing & Navigation

- **App Router:**
  Use Next.js 15+ `/app` directory routing for layouts, nested routes, loading/error states.

- **Route Groups:**
  Use route groups like `(auth)` and `(dashboard)` for logical organization.

- **Dynamic Routes:**
  Use `[param]` or `[[...slug]]` for dynamic segments.

- **Middleware:**
  Implement `/middleware.ts` for:
  - Authentication checks and redirects
  - Rate limiting handling
  - Request logging
  - CORS handling

---

## 7. Testing

- **Unit & Integration Tests:**
  Use Jest and React Testing Library for component and hook testing.

- **End-to-End (E2E):**
  Use Playwright for comprehensive E2E testing including auth flows.

- **API Integration Tests:**
  Test API integration with mock FastAPI responses.

- **Coverage:**
  Target >90% coverage. Integrate with CI/CD pipeline.

- **Test Structure:**
  ```
  /__tests__
    /components
    /pages
    /hooks
    /e2e
  ```

---

## 8. Performance & Optimization

- **Next.js Built-in Features:**
  - **Flexible Routing:** Comprehensive support for static, dynamic, nested, catch-all, grouped, and conditional routes
  - **File System-Based Structure:** Intuitive project structure with file colocation and organized layouts
  - **Advanced Layouts:** Root, nested, and parallel layouts for reusable UI patterns
  - **Rich Metadata & Navigation:** Built-in metadata handling and programmatic navigation for SEO
  - **Error & Loading Handling:** Fine-grained error boundaries, loading UIs, and recovery strategies
  - **API Route Handlers:** Direct support for HTTP methods and dynamic route handlers
  - **Built-in Middleware & Caching:** Request interception and caching for security and performance
  - **Versatile Rendering:** Static, dynamic, client-side, server-side, and streaming rendering support

- **Form Actions with FastAPI Backend:**
  - **useActionState:** Most powerful choice for form actions with robust state management and error handling
  - **useOptimistic:** Combine with useActionState for instant UI feedback on interactive features
  - **useFormStatus:** Use for basic loading states when fine-grained control isn't needed
  - **Best Practice:** Use useActionState + useOptimistic for optimal UX with FastAPI integration

- **Image Optimization:**
  Use Next.js `<Image />` component for responsive, lazy-loaded images.

- **Code Splitting:**
  Use dynamic imports for large/lazy components and route-based splitting.

- **Static Generation & SSR:**
  Use `generateStaticParams`, server components, and caching for optimal page speed.

- **Bundle Analysis:**
  Regularly run `next build && npm run analyze` and address large bundles.

---

## 9. Accessibility & UX

- **Accessibility:**
  - Use semantic HTML elements
  - Test with screen readers
  - Validate with axe or eslint-plugin-jsx-a11y
  - Ensure proper ARIA labels and roles

- **Responsive Design:**
  Ensure mobile-first design with tablet and desktop support.

- **Error Boundaries:**
  Implement error boundaries for robust UX and error recovery.

- **Loading States:**
  Provide clear loading indicators and skeleton screens.

---

## 10. FastAPI Backend Integration

- **OpenAPI Client Generation:**
  - Generate TypeScript types from FastAPI's OpenAPI schema
  - Use tools like `openapi-typescript` or `swagger-codegen`
  - Keep generated types in sync with backend changes

- **Authentication Compatibility:**
  - Support both JWT and session-based flows as provided by FastAPI
  - Handle token refresh automatically
  - Implement proper logout with backend session invalidation

- **Error Handling Consistency:**
  - Expect and handle JSON error responses per FastAPI conventions
  - Map HTTP status codes to appropriate UI states
  - Handle validation errors from Pydantic models

- **API Versioning:**
  - Always consume backend APIs with `/api/v1/` or `/api/v2/` prefixes
  - Handle version deprecation gracefully

- **CORS & Security:**
  - Backend must whitelist frontend domain
  - Handle potential CORS errors gracefully
  - Respect security headers set by FastAPI backend

- **Rate Limiting:**
  - Handle 429 responses from FastAPI rate limiting
  - Implement client-side rate limiting awareness
  - Show appropriate user feedback for rate limit hits

---

## 11. Development Workflow

- **Environment Setup:**
  - Use `.env.local` for local development variables
  - Set up proper TypeScript configuration
  - Configure ESLint and Prettier

- **Code Quality:**
  - Enforce code reviews for all PRs
  - Use GitHub Actions for automated checks
  - Run tests in CI/CD pipeline

- **Performance Monitoring:**
  - Use Lighthouse for performance audits
  - Monitor Core Web Vitals
  - Use Vercel Analytics or similar tools

- **Dependency Management:**
  - Regularly update dependencies
  - Track breaking changes in Next.js releases
  - Use package manager lock files

---

> **Following these rules will help you build scalable, maintainable, and high-performance Next.js 15+ frontends fully compatible with the current FastAPI backend implementation.**
