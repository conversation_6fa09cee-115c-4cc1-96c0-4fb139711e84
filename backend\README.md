# FastAPI Backend

A production-ready FastAPI backend with comprehensive authentication, Redis caching, monitoring, and Next.js compatibility. Built following enterprise-grade patterns with async/await throughout, role-based access control, and extensive observability features.

## 🚀 Project Overview

This FastAPI application provides a robust foundation for modern web applications with:

- **Full-stack authentication** supporting both JWT tokens and browser sessions
- **Redis-based caching** with intelligent cache management and warming
- **Comprehensive monitoring** with health checks, performance metrics, and system monitoring
- **API versioning** with multiple versioning strategies (header, URL, query parameter)
- **Clean logging** with configurable console output and detailed file logging
- **Next.js compatibility** with proper CORS, cookie handling, and error responses
- **Production-ready** with security headers, rate limiting, and structured error handling

## ✨ Key Features

### 🔐 Authentication & Authorization

- **JWT Authentication** with short-lived access tokens and refresh tokens
- **Session-based Authentication** for browser compatibility (Next.js SSR/CSR)
- **Email Verification** using Resend API with secure token management
- **Role-Based Access Control (RBAC)** with granular permissions
- **Password Security** with bcrypt hashing and password change tracking
- **Rate Limiting** on authentication endpoints to prevent abuse

### 💾 Database & Caching

- **PostgreSQL** with SQLAlchemy 2.0+ and async support (asyncpg)
- **Redis Caching** with connection pooling and intelligent serialization
- **Database Migrations** with Alembic and auto-generation
- **Connection Management** with proper session handling and cleanup
- **Cache Decorators** for easy function result caching

### 📊 Monitoring & Observability

- **Health Check Endpoints** with database, cache, and system checks
- **Performance Monitoring** with query timing and optimization recommendations
- **System Metrics** including CPU, memory, disk usage via psutil
- **API Versioning Info** and feature flag reporting
- **Structured Logging** with JSON formatting for production
- **Clean Console Output** with configurable verbosity levels

### 🛡️ Security & Middleware

- **Security Headers** (HSTS, X-Frame-Options, CSP, etc.)
- **CORS Configuration** for trusted frontend origins
- **Input Validation** with Pydantic models and type hints
- **Exception Handling** with consistent error responses
- **GZip Compression** for response optimization
- **Request/Response Logging** with performance tracking

## 🏗️ Architecture

The application follows a layered architecture with clear separation of concerns:

```mermaid
graph TB
    subgraph "Client Layer"
        A[Next.js Frontend]
        B[Mobile App]
        C[API Client]
    end

    subgraph "API Gateway Layer"
        D[FastAPI Application]
        E[CORS Middleware]
        F[Rate Limiting]
        G[Security Headers]
        H[API Versioning]
    end

    subgraph "Authentication Layer"
        I[JWT Auth]
        J[Session Auth]
        K[RBAC Permissions]
        L[Email Verification]
    end

    subgraph "Business Logic Layer"
        M[User Management]
        N[Authentication Routes]
        O[Monitoring Routes]
        P[Item Management]
    end

    subgraph "Data Access Layer"
        Q[CRUD Operations]
        R[SQLAlchemy Models]
        S[Pydantic Schemas]
    end

    subgraph "Infrastructure Layer"
        T[(PostgreSQL)]
        U[(Redis Cache)]
        V[Email Service]
        W[Logging System]
    end

    A --> D
    B --> D
    C --> D

    D --> E
    E --> F
    F --> G
    G --> H

    H --> I
    H --> J
    I --> K
    J --> K
    K --> L

    K --> M
    K --> N
    K --> O
    K --> P

    M --> Q
    N --> Q
    O --> Q
    P --> Q

    Q --> R
    R --> S

    Q --> T
    Q --> U
    L --> V
    D --> W
```

### Component Responsibilities

- **API Gateway**: Request routing, middleware processing, and response handling
- **Authentication**: Token validation, session management, and permission checking
- **Business Logic**: Core application features and domain logic
- **Data Access**: Database operations, caching, and data validation
- **Infrastructure**: External services, storage, and system resources

## 🚀 Quick Start

### Prerequisites

- **Python 3.11+** with [uv](https://github.com/astral-sh/uv) package manager
- **PostgreSQL 14+** database server
- **Redis 6+** server (for caching)
- **Git** for version control

### Local Development Setup

1. **Clone and setup the project**:

   ```bash
   git clone <repository-url>
   cd backend
   uv sync  # Install dependencies and create virtual environment
   ```

2. **Setup environment variables**:

   ```bash
   cp .env.example .env
   ```

   Edit `.env` with your configuration:

   ```bash
   # Database
   DATABASE_URL=postgresql://user:password@localhost/webapp

   # Security
   SECRET_KEY=your-super-secret-key-here

   # Email (optional for development)
   RESEND_API_KEY=your-resend-api-key

   # Logging (optional)
   LOG_LEVEL_CONSOLE=WARNING  # Clean console output
   LOG_LEVEL_FILE=INFO        # Detailed file logs
   ```

3. **Setup databases**:

   ```bash
   # PostgreSQL
   createdb webapp

   # Redis (if not using Docker)
   # Make sure Redis is running on localhost:6379
   redis-server
   ```

4. **Run database migrations**:

   ```bash
   uv run alembic upgrade head
   ```

5. **Start the development server**:
   ```bash
   uv run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
   ```

### Using Docker Compose (Alternative)

1. **Setup environment**:

   ```bash
   cp .env.example .env
   # Edit .env as needed
   ```

2. **Start all services**:

   ```bash
   docker-compose up -d
   ```

3. **Run migrations**:
   ```bash
   docker-compose exec backend alembic upgrade head
   ```

### Verify Installation

Visit these URLs to confirm everything is working:

- **API Health**: http://localhost:8000/health
- **Swagger Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **Monitoring**: http://localhost:8000/api/v1/monitoring/health

## 📚 API Documentation

The API provides comprehensive documentation through multiple interfaces:

### Interactive Documentation

- **Swagger UI**: http://localhost:8000/docs - Interactive API explorer with request/response examples
- **ReDoc**: http://localhost:8000/redoc - Clean, responsive API documentation
- **OpenAPI Schema**: http://localhost:8000/api/v1/openapi.json - Machine-readable API specification

### Key API Endpoints

| Endpoint                     | Method | Description              | Authentication |
| ---------------------------- | ------ | ------------------------ | -------------- |
| `/health`                    | GET    | Basic health check       | None           |
| `/api/v1/auth/register`      | POST   | User registration        | None           |
| `/api/v1/auth/login`         | POST   | User login               | None           |
| `/api/v1/auth/refresh`       | POST   | Refresh access token     | Refresh Token  |
| `/api/v1/auth/logout`        | POST   | User logout              | Access Token   |
| `/api/v1/users/me`           | GET    | Get current user profile | Access Token   |
| `/api/v1/users`              | GET    | List users (admin only)  | Admin Token    |
| `/api/v1/monitoring/health`  | GET    | Detailed health check    | None           |
| `/api/v1/monitoring/metrics` | GET    | Performance metrics      | None           |

### API Versioning

The API supports multiple versioning strategies:

```bash
# Header-based versioning
curl -H "API-Version: v1" http://localhost:8000/api/users/me

# URL path versioning (default)
curl http://localhost:8000/api/v1/users/me

# Query parameter versioning
curl http://localhost:8000/api/users/me?version=v1
```

## 🔐 Authentication Flow

The application supports dual authentication modes for maximum compatibility:

### JWT + Session Authentication Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant A as FastAPI
    participant D as Database
    participant R as Redis Cache
    participant E as Email Service

    Note over C,E: User Registration Flow
    C->>A: POST /auth/register
    A->>D: Create user (unverified)
    A->>E: Send verification email
    A-->>C: 201 Created (user created)

    Note over C,E: Email Verification
    C->>A: GET /auth/verify-email?token=xxx
    A->>D: Verify token & activate user
    A-->>C: 200 OK (email verified)

    Note over C,E: Login Flow
    C->>A: POST /auth/login
    A->>D: Validate credentials
    A->>D: Create session record
    A->>R: Cache user permissions
    A-->>C: 200 OK (access_token, refresh_token, session_id)

    Note over C,E: Authenticated Requests
    C->>A: GET /users/me (Bearer token)
    A->>R: Check cached permissions
    alt Cache Hit
        R-->>A: User permissions
    else Cache Miss
        A->>D: Query user permissions
        A->>R: Cache permissions
    end
    A-->>C: 200 OK (user data)

    Note over C,E: Token Refresh
    C->>A: POST /auth/refresh (refresh_token)
    A->>D: Validate refresh token & session
    A-->>C: 200 OK (new access_token)

    Note over C,E: Logout
    C->>A: POST /auth/logout
    A->>D: Invalidate session
    A->>R: Clear cached permissions
    A-->>C: 200 OK (logged out)
```

### Authentication Methods

1. **JWT Tokens** (Recommended for SPAs):

   ```bash
   # Login to get tokens
   curl -X POST http://localhost:8000/api/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>", "password": "password"}'

   # Use access token for requests
   curl -H "Authorization: Bearer <access_token>" \
     http://localhost:8000/api/v1/users/me
   ```

2. **Session Cookies** (For Next.js SSR):

   ```bash
   # Login with session cookie
   curl -X POST http://localhost:8000/api/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>", "password": "password"}' \
     -c cookies.txt

   # Use session cookie for requests
   curl -b cookies.txt http://localhost:8000/api/v1/users/me
   ```

### Role-Based Access Control (RBAC)

Users have roles with specific permissions:

- **USER**: Basic user permissions (default)
- **ADMIN**: Administrative permissions
- **SUPERUSER**: Full system access

Permissions are cached in Redis for optimal performance.

## 📝 Logging Configuration

The application features a sophisticated logging system with clean console output:

### Console vs File Logging

- **Console**: Shows only important messages (warnings/errors) by default
- **Files**: Detailed logs with full context saved to `logs/` directory

### Configuration

```bash
# Environment variables
LOG_LEVEL_CONSOLE=WARNING  # Console verbosity: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL_FILE=INFO        # File verbosity: DEBUG, INFO, WARNING, ERROR

# Test different levels
python scripts/test_logging.py

# Temporary verbose console
LOG_LEVEL_CONSOLE=INFO uvicorn app.main:app --reload
```

### Log Files

- `logs/app.log` - All application logs (rotated, 10MB max)
- `logs/error.log` - Error-level logs only
- `logs/security.log` - Security events (production)

See [docs/LOGGING.md](docs/LOGGING.md) for detailed configuration.

## 💾 Database & Caching

### PostgreSQL with SQLAlchemy 2.0+

- **Async Support**: Full async/await with asyncpg driver
- **Connection Pooling**: Optimized connection management
- **Migrations**: Alembic with auto-generation
- **Models**: UUID primary keys, timestamps, relationships

### Redis Caching System

- **Intelligent Serialization**: JSON for simple types, pickle for complex objects
- **Connection Pooling**: Efficient Redis connection management
- **Cache Decorators**: Easy function result caching with `@cached(ttl=300)`
- **Pattern Invalidation**: Clear related cache entries
- **Health Monitoring**: Cache statistics and performance metrics

```python
# Using cache decorator
@cached(ttl=600, key_prefix="user")
async def get_user_permissions(user_id: str):
    # Function result cached for 10 minutes
    return await fetch_permissions(user_id)
```

## 📊 Monitoring & Health Checks

### Available Endpoints

- `/health` - Basic health check
- `/api/v1/monitoring/health` - Comprehensive health with database/cache checks
- `/api/v1/monitoring/metrics` - Performance metrics and system stats
- `/api/v1/monitoring/info` - Application info and feature flags

### Metrics Included

- **System**: CPU, memory, disk usage
- **Database**: Connection pool status, query performance
- **Cache**: Hit/miss ratios, connection status
- **API**: Response times, error rates
- **Performance**: Slow query detection, optimization recommendations

## 🧪 Development

### Running Tests

```bash
# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov=app --cov-report=html

# Run specific test file
uv run pytest tests/test_auth.py

# Run tests with verbose output
uv run pytest -v
```

### Code Quality

```bash
# Format code
uv run black .
uv run isort .

# Lint code
uv run flake8 .

# Type checking
uv run mypy app/
```

### Database Operations

```bash
# Create new migration
uv run alembic revision --autogenerate -m "Description"

# Apply migrations
uv run alembic upgrade head

# Rollback migration
uv run alembic downgrade -1

# View migration history
uv run alembic history
```

### Useful Development Commands

```bash
# View logs in real-time
Get-Content logs/app.log -Wait -Tail 10

# Test logging levels
python scripts/test_logging.py

# Clear Redis cache
redis-cli FLUSHDB

# Check Redis connection
redis-cli ping
```

## 🚀 Production Deployment

### Environment Configuration

```bash
# Production environment variables
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL_CONSOLE=ERROR
LOG_LEVEL_FILE=INFO

# Security
SECRET_KEY=<strong-random-key>
BACKEND_CORS_ORIGINS=["https://yourdomain.com"]

# Database
DATABASE_URL=***************************************/webapp

# Redis
REDIS_URL=redis://redis-host:6379/0

# Email
RESEND_API_KEY=<your-production-key>
FROM_EMAIL=<EMAIL>
```

### Production Considerations

1. **Security**:

   - Use strong, unique SECRET_KEY
   - Enable HTTPS with proper certificates
   - Configure security headers
   - Set up rate limiting
   - Regular security updates

2. **Performance**:

   - Use connection pooling for database
   - Configure Redis for persistence
   - Enable GZip compression
   - Monitor slow queries
   - Set up caching strategies

3. **Monitoring**:

   - Set up application monitoring (Sentry, DataDog)
   - Configure log aggregation
   - Monitor health check endpoints
   - Set up alerts for errors/performance

4. **Deployment**:
   - Use Docker containers
   - Set up CI/CD pipelines
   - Database migration automation
   - Blue-green deployment strategy

### Project Structure

```
backend/
├── app/                    # Application code
│   ├── api/               # API routes and endpoints
│   │   ├── deps.py        # Dependency injection
│   │   ├── main.py        # API router setup
│   │   └── v1/            # API version 1
│   │       ├── api.py     # Route registration
│   │       └── routes/    # Individual route modules
│   ├── core/              # Core functionality
│   │   ├── config.py      # Settings and configuration
│   │   ├── security.py    # Authentication utilities
│   │   ├── cache.py       # Redis caching system
│   │   ├── logging.py     # Logging configuration
│   │   ├── middleware.py  # Custom middleware
│   │   └── exceptions.py  # Exception handlers
│   ├── crud/              # Database operations
│   ├── database/          # Database configuration
│   ├── models/            # SQLAlchemy models
│   ├── schemas/           # Pydantic schemas
│   ├── utils/             # Utility functions
│   └── tests/             # Test files
├── alembic/               # Database migrations
├── docs/                  # Documentation
├── logs/                  # Log files (created at runtime)
├── scripts/               # Utility scripts
├── .env.example           # Environment template
├── pyproject.toml         # Python dependencies
└── README.md              # This file
```

## 📋 Contributing

1. Follow the coding standards in [`.augment/rules/fastapi-rules.md`](.augment/rules/fastapi-rules.md)
2. Write comprehensive tests for new features
3. Update documentation for API changes
4. Run code quality checks: `black . && flake8 . && isort .`
5. Ensure all tests pass: `uv run pytest`
6. Update the README if adding new features

## 📄 License

MIT License - see LICENSE file for details.
