"""Item model."""
from sqlalchemy import <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.models.base import BaseModel

class Blog(BaseModel):
    __tablename__ = "blogs"
    
    title = Column(String, index=True)
    description = Column(Text)
    content = Column(Text)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    
    owner = relationship("User", back_populates="blogs")