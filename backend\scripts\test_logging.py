#!/usr/bin/env python3
"""
Script to test different logging levels and demonstrate clean console output.

Usage:
    python scripts/test_logging.py
    
Environment variables to control logging:
    LOG_LEVEL_CONSOLE=WARNING  # Only warnings and errors in console
    LOG_LEVEL_CONSOLE=INFO     # Show info, warnings, and errors in console
    LOG_LEVEL_CONSOLE=DEBUG    # Show all messages in console
"""

import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from app.core.logging import setup_logging, get_logger

def test_logging_levels():
    """Test different logging levels."""
    
    print("🔧 Current logging configuration:")
    print(f"   Console level: {os.getenv('LOG_LEVEL_CONSOLE', 'WARNING')}")
    print(f"   File level: {os.getenv('LOG_LEVEL_FILE', 'INFO')}")
    print()
    
    # Setup logging
    setup_logging()
    logger = get_logger("test")
    
    print("📝 Testing log messages:")
    print("   (Check console vs logs/app.log to see the difference)")
    print()
    
    logger.debug("🐛 DEBUG: This is a debug message")
    logger.info("ℹ️  INFO: This is an info message") 
    logger.warning("⚠️  WARNING: This is a warning message")
    logger.error("❌ ERROR: This is an error message")
    
    print()
    print("✅ Test complete!")
    print("   - Console shows only WARNING and ERROR (by default)")
    print("   - File logs/app.log contains ALL messages")
    print()
    print("💡 To change console verbosity:")
    print("   export LOG_LEVEL_CONSOLE=INFO    # Show more in console")
    print("   export LOG_LEVEL_CONSOLE=DEBUG   # Show everything in console")
    print("   export LOG_LEVEL_CONSOLE=ERROR   # Show only errors in console")

if __name__ == "__main__":
    test_logging_levels()
