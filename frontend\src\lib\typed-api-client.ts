// Typed API client using generated OpenAPI types
import axios, { AxiosInstance, AxiosError } from "axios";
import { config } from "./config";
import type { paths, components } from "@/types/api";

// Extract types from OpenAPI schema
export type ApiPaths = paths;
export type ApiComponents = components;

// Specific schema types
export type UserResponse = components["schemas"]["UserResponse"];
export type UserCreate = components["schemas"]["UserCreate"];
export type UserUpdate = components["schemas"]["UserUpdate"];
export type Token = components["schemas"]["Token"];
export type HTTPValidationError = components["schemas"]["HTTPValidationError"];
export type ValidationError = components["schemas"]["ValidationError"];

// Operation types for type-safe API calls
export type RegisterOperation = paths["/api/v1/auth/register"]["post"];
export type LoginOperation = paths["/api/v1/auth/login"]["post"];
export type RefreshOperation = paths["/api/v1/auth/refresh"]["post"];
export type VerifyEmailOperation = paths["/api/v1/auth/verify-email"]["post"];
export type ForgotPasswordOperation = paths["/api/v1/auth/forgot-password"]["post"];
export type ResetPasswordOperation = paths["/api/v1/auth/reset-password"]["post"];
export type GetCurrentUserOperation = paths["/api/v1/users/me"]["get"];
export type UpdateCurrentUserOperation = paths["/api/v1/users/me"]["put"];

// Type-safe request/response types
export type RegisterRequest = RegisterOperation["requestBody"]["content"]["application/json"];
export type RegisterResponse = RegisterOperation["responses"][201]["content"]["application/json"];

export type LoginRequest = LoginOperation["requestBody"]["content"]["application/x-www-form-urlencoded"];
export type LoginResponse = LoginOperation["responses"][200]["content"]["application/json"];

export type RefreshRequest = RefreshOperation["parameters"]["query"];
export type RefreshResponse = RefreshOperation["responses"][200]["content"]["application/json"];

export type GetCurrentUserResponse = GetCurrentUserOperation["responses"][200]["content"]["application/json"];

export type UpdateCurrentUserRequest = UpdateCurrentUserOperation["requestBody"]["content"]["application/json"];
export type UpdateCurrentUserResponse = UpdateCurrentUserOperation["responses"][200]["content"]["application/json"];

// Error response type
export type ApiErrorResponse = HTTPValidationError;

// Type-safe API client class
export class TypedApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: config.API_BASE_URL,
      timeout: 10000,
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true,
    });

    // Request interceptor for authentication
    this.client.interceptors.request.use((config) => {
      const token = this.getAccessToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      async (error: AxiosError<ApiErrorResponse>) => {
        if (error.response?.status === 401) {
          // Try to refresh token
          const refreshed = await this.refreshToken();
          if (refreshed && error.config) {
            // Retry original request
            return this.client.request(error.config);
          }
          // Redirect to login if refresh fails
          this.clearTokens();
          window.location.href = "/login";
        }
        return Promise.reject(error);
      }
    );
  }

  // Authentication methods
  async register(data: RegisterRequest): Promise<RegisterResponse> {
    const response = await this.client.post<RegisterResponse>("/api/v1/auth/register", data);
    return response.data;
  }

  async login(credentials: { email: string; password: string }): Promise<LoginResponse> {
    // Convert to form data as expected by backend
    const formData = new URLSearchParams();
    formData.append("username", credentials.email);
    formData.append("password", credentials.password);

    const response = await this.client.post<LoginResponse>("/api/v1/auth/login", formData, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });

    // Store tokens
    this.setTokens(response.data);
    return response.data;
  }

  async refreshToken(): Promise<boolean> {
    try {
      const refreshToken = this.getRefreshToken();
      if (!refreshToken) return false;

      const response = await this.client.post<RefreshResponse>(
        `/api/v1/auth/refresh?refresh_token=${encodeURIComponent(refreshToken)}`
      );

      this.setTokens(response.data);
      return true;
    } catch {
      return false;
    }
  }

  async logout(): Promise<void> {
    try {
      await this.client.post("/api/v1/auth/logout");
    } finally {
      this.clearTokens();
    }
  }

  async verifyEmail(token: string): Promise<void> {
    await this.client.post(`/api/v1/auth/verify-email?token=${encodeURIComponent(token)}`);
  }

  async forgotPassword(email: string): Promise<void> {
    await this.client.post(`/api/v1/auth/forgot-password?email=${encodeURIComponent(email)}`);
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    await this.client.post(
      `/api/v1/auth/reset-password?token=${encodeURIComponent(token)}&new_password=${encodeURIComponent(newPassword)}`
    );
  }

  // User methods
  async getCurrentUser(): Promise<GetCurrentUserResponse> {
    const response = await this.client.get<GetCurrentUserResponse>("/api/v1/users/me");
    return response.data;
  }

  async updateCurrentUser(data: UpdateCurrentUserRequest): Promise<UpdateCurrentUserResponse> {
    const response = await this.client.put<UpdateCurrentUserResponse>("/api/v1/users/me", data);
    return response.data;
  }

  // Token management
  private getAccessToken(): string | null {
    if (typeof window === "undefined") return null;
    return localStorage.getItem("access_token");
  }

  private getRefreshToken(): string | null {
    if (typeof window === "undefined") return null;
    return localStorage.getItem("refresh_token");
  }

  private setTokens(tokens: Token): void {
    if (typeof window === "undefined") return;
    localStorage.setItem("access_token", tokens.access_token);
    localStorage.setItem("refresh_token", tokens.refresh_token);
  }

  private clearTokens(): void {
    if (typeof window === "undefined") return;
    localStorage.removeItem("access_token");
    localStorage.removeItem("refresh_token");
  }
}

// Export singleton instance
export const typedApiClient = new TypedApiClient();

// Type comparison utilities for validation
export function compareTypes() {
  // This function helps identify discrepancies between manual types and OpenAPI types
  console.log("=== Type Comparison Report ===");
  
  // Compare User types
  console.log("Manual User vs OpenAPI UserResponse:");
  console.log("Manual User has 'updated_at: string' but OpenAPI has 'updated_at?: string | null'");
  console.log("Manual User missing 'role: string' field from OpenAPI");
  
  // Compare AuthTokens types
  console.log("Manual AuthTokens vs OpenAPI Token:");
  console.log("Types match perfectly ✓");
  
  // Compare error types
  console.log("Manual ApiError vs OpenAPI HTTPValidationError:");
  console.log("Manual has 'status_code: number' but OpenAPI uses HTTP status codes");
  console.log("Manual has 'detail: string' but OpenAPI has 'detail?: ValidationError[]'");
}
