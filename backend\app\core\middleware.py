"""Middleware for security, CORS, rate limiting, and performance monitoring."""
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from slowapi.middleware import SlowAPIMiddleware
from secure import Secure

from app.core.config import settings
from app.core.performance import PerformanceMiddleware
from app.core.logging import get_logger, log_rate_limit_exceeded

logger = get_logger("middleware")

# Rate limiter
limiter = Limiter(key_func=get_remote_address)

# Security headers - we'll add them manually in middleware
# secure_headers = Secure()


def setup_middleware(app: FastAPI) -> None:
    """Setup all middleware for the FastAPI application."""

    # Performance monitoring middleware (should be first)
    app.add_middleware(PerformanceMiddleware, slow_request_threshold=1.0)

    # Security headers middleware
    @app.middleware("http")
    async def add_security_headers(request: Request, call_next):
        response = await call_next(request)
        # Add security headers manually
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        if request.url.scheme == "https":
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        return response
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.BACKEND_CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Custom rate limit handler
    async def rate_limit_handler(request: Request, exc: RateLimitExceeded):
        """Custom rate limit exceeded handler."""
        client_ip = get_remote_address(request)
        endpoint = str(request.url.path)

        # Log rate limit exceeded
        log_rate_limit_exceeded(client_ip, endpoint)

        return await _rate_limit_exceeded_handler(request, exc)

    # Rate limiting middleware
    app.state.limiter = limiter
    app.add_exception_handler(RateLimitExceeded, rate_limit_handler)
    app.add_middleware(SlowAPIMiddleware)
    
    # Trusted host middleware (for production)
    if settings.ENVIRONMENT == "production":
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["yourdomain.com", "*.yourdomain.com"]
        )


def get_rate_limiter():
    """Get the rate limiter instance."""
    return limiter
