"""Blog schemas."""
from pydantic import BaseModel
from typing import Optional

from app.schemas.base import BaseSchema


class BlogBase(BaseModel):
    title: str
    description: Optional[str] = None
    content: Optional[str] = None


class BlogCreate(BlogBase):
    pass


class BlogUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    content: Optional[str] = None


class BlogResponse(BaseSchema, BlogBase):
    owner_id: str