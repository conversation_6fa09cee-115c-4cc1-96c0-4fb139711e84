"""User model."""
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, DateTime, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.models.base import BaseModel
from app.core.constants import UserRole

class User(BaseModel):
    __tablename__ = "users"

    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    is_superuser = Column(Boolean, default=False)
    role = Column(String, default=UserRole.USER)

    # Profile information
    avatar_url = Column(String)
    bio = Column(Text)

    # Timestamps for security
    last_login = Column(DateTime(timezone=True))
    password_changed_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    blogs = relationship("Blog", back_populates="owner")
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    email_verifications = relationship("EmailVerification", back_populates="user", cascade="all, delete-orphan")

    def has_permission(self, permission: str) -> bool:
        """Check if user has specific permission."""
        from app.core.constants import Permission, UserRole

        if self.is_superuser or self.role == UserRole.ADMIN:
            return True

        # Regular user permissions
        if self.role == UserRole.USER:
            if permission == Permission.READ:
                return True
            return False

        return False

    def is_admin(self) -> bool:
        """Check if user is admin."""
        return self.role == UserRole.ADMIN or self.is_superuser