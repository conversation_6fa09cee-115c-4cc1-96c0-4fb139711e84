// Enhanced API client with hybrid caching
import { typedApiClient } from "./typed-api-client";
import { hybridCache, userCache } from "./hybrid-cache-client";
import type { UserResponse } from "@/types/api";

export class EnhancedApiClient {
  // Get current user with hybrid caching
  async getCurrentUser(forceRefresh = false): Promise<UserResponse | null> {
    const cacheKey = 'current_user';
    
    if (forceRefresh) {
      await userCache.remove(cacheKey);
    }

    return await userCache.get(cacheKey, async () => {
      // This will hit the backend, which checks its Redis cache first
      return await typedApiClient.getCurrentUser();
    });
  }

  // Get user preferences with caching
  async getUserPreferences(userId: string): Promise<any> {
    const cacheKey = `user_preferences_${userId}`;
    
    return await hybridCache.get(cacheKey, async () => {
      const response = await fetch(`/api/v1/users/${userId}/preferences`);
      return response.json();
    });
  }

  // Update user and invalidate cache
  async updateCurrentUser(data: any): Promise<UserResponse> {
    const result = await typedApiClient.updateCurrentUser(data);
    
    // Invalidate user cache across all layers
    await userCache.remove('current_user');
    
    // Optionally pre-populate cache with new data
    await userCache.set('current_user', result);
    
    return result;
  }

  // Login with cache setup
  async login(credentials: { email: string; password: string }) {
    const result = await typedApiClient.login(credentials);
    
    // Clear any existing user cache on login
    await userCache.clear();
    
    return result;
  }

  // Logout with cache cleanup
  async logout() {
    await typedApiClient.logout();
    
    // Clear all caches on logout
    await userCache.clear();
    await hybridCache.clear();
  }
}

export const enhancedApiClient = new EnhancedApiClient();
