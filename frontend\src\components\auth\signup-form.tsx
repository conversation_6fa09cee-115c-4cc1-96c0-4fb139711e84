"use client";

import { useActionState, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import {
  GalleryVerticalEnd,
  Loader2,
  Eye,
  EyeOff,
  Check,
  X,
  AlertCircle,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { signupAction } from "@/lib/actions/auth-actions";
import Link from "next/link";
import type { FormState } from "@/types";

const initialState: FormState = {
  success: false,
  message: "",
  errors: {},
};

// Password strength checker
const checkPasswordStrength = (password: string) => {
  const checks = {
    length: password.length >= 8,
    uppercase: /[A-Z]/.test(password),
    lowercase: /[a-z]/.test(password),
    number: /\d/.test(password),
  };

  const score = Object.values(checks).filter(Boolean).length;
  return { checks, score };
};

export function SignupForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [state, formAction, isPending] = useActionState(
    signupAction,
    initialState
  );
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const passwordStrength = checkPasswordStrength(password);
  const passwordsMatch =
    password === confirmPassword && confirmPassword.length > 0;

  // Handle successful signup with improved UX
  useEffect(() => {
    if (state.success) {
      // Show success toast with email verification instructions
      toast.success(
        state.message ||
          "Registration successful! Please check your email to verify your account.",
        {
          duration: 4000, // Show for 4 seconds
          description: "You'll be redirected to the login page shortly.",
        }
      );

      // Redirect to login page after showing the success message
      const redirectTimer = setTimeout(() => {
        router.push("/login?message=signup-success");
      }, 3000); // 3 second delay to allow users to read the notification

      // Cleanup timer if component unmounts
      return () => clearTimeout(redirectTimer);
    }
  }, [state.success, state.message, router]);

  // Handle form errors with better user feedback
  useEffect(() => {
    if (!state.success && state.message && state.message !== "") {
      // Don't show toast for validation errors, only for server errors
      if (!state.errors || Object.keys(state.errors).length === 0) {
        toast.error(state.message);
      }
    }
  }, [state.success, state.message, state.errors]);

  // Helper function to get user-friendly error messages
  const getFieldError = (fieldName: string): string | undefined => {
    if (state.errors?.[fieldName]) {
      return state.errors[fieldName][0];
    }
    return undefined;
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <form action={formAction}>
        <div className="flex flex-col gap-6">
          <div className="flex flex-col items-center gap-2">
            <Link
              href="/"
              className="flex flex-col items-center gap-2 font-medium"
            >
              <div className="flex size-8 items-center justify-center rounded-md bg-primary text-primary-foreground">
                <GalleryVerticalEnd className="size-6" />
              </div>
              <span className="sr-only">WebApp</span>
            </Link>
            <h1 className="text-xl font-bold">Create your account</h1>
            <div className="text-center text-sm">
              Already have an account?{" "}
              <Link href="/login" className="underline underline-offset-4">
                Sign in
              </Link>
            </div>
          </div>

          {!state.success && state.message && !state.errors && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{state.message}</AlertDescription>
            </Alert>
          )}

          {state.success && state.message && (
            <Alert>
              <AlertDescription>{state.message}</AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col gap-6">
            <div className="grid gap-3">
              <Label htmlFor="full_name">Full Name (Optional)</Label>
              <Input
                id="full_name"
                name="full_name"
                type="text"
                placeholder="John Doe"
                disabled={isPending}
                className={cn(
                  getFieldError("full_name") &&
                    "border-destructive focus-visible:ring-destructive"
                )}
              />
              {getFieldError("full_name") && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {getFieldError("full_name")}
                </p>
              )}
            </div>

            <div className="grid gap-3">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                required
                disabled={isPending}
                className={cn(
                  getFieldError("email") &&
                    "border-destructive focus-visible:ring-destructive"
                )}
              />
              {getFieldError("email") && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {getFieldError("email")}
                </p>
              )}
            </div>

            <div className="grid gap-3">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  required
                  disabled={isPending}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className={cn(
                    "pr-10",
                    getFieldError("password") &&
                      "border-destructive focus-visible:ring-destructive"
                  )}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isPending}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showPassword ? "Hide password" : "Show password"}
                  </span>
                </Button>
              </div>

              {/* Password strength indicator */}
              {password.length > 0 && (
                <div className="space-y-2">
                  <div className="flex gap-1">
                    {[1, 2, 3, 4].map((level) => (
                      <div
                        key={level}
                        className={cn(
                          "h-1 flex-1 rounded-full transition-colors",
                          passwordStrength.score >= level
                            ? passwordStrength.score === 4
                              ? "bg-green-500"
                              : passwordStrength.score === 3
                                ? "bg-yellow-500"
                                : "bg-red-500"
                            : "bg-muted"
                        )}
                      />
                    ))}
                  </div>
                  <div className="space-y-1 text-xs">
                    <div
                      className={cn(
                        "flex items-center gap-1",
                        passwordStrength.checks.length
                          ? "text-green-600"
                          : "text-muted-foreground"
                      )}
                    >
                      {passwordStrength.checks.length ? (
                        <Check className="h-3 w-3" />
                      ) : (
                        <X className="h-3 w-3" />
                      )}
                      At least 8 characters
                    </div>
                    <div
                      className={cn(
                        "flex items-center gap-1",
                        passwordStrength.checks.uppercase
                          ? "text-green-600"
                          : "text-muted-foreground"
                      )}
                    >
                      {passwordStrength.checks.uppercase ? (
                        <Check className="h-3 w-3" />
                      ) : (
                        <X className="h-3 w-3" />
                      )}
                      One uppercase letter
                    </div>
                    <div
                      className={cn(
                        "flex items-center gap-1",
                        passwordStrength.checks.lowercase
                          ? "text-green-600"
                          : "text-muted-foreground"
                      )}
                    >
                      {passwordStrength.checks.lowercase ? (
                        <Check className="h-3 w-3" />
                      ) : (
                        <X className="h-3 w-3" />
                      )}
                      One lowercase letter
                    </div>
                    <div
                      className={cn(
                        "flex items-center gap-1",
                        passwordStrength.checks.number
                          ? "text-green-600"
                          : "text-muted-foreground"
                      )}
                    >
                      {passwordStrength.checks.number ? (
                        <Check className="h-3 w-3" />
                      ) : (
                        <X className="h-3 w-3" />
                      )}
                      One number
                    </div>
                  </div>
                </div>
              )}

              {getFieldError("password") && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {getFieldError("password")}
                </p>
              )}
            </div>

            <div className="grid gap-3">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  required
                  disabled={isPending}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className={cn(
                    "pr-10",
                    getFieldError("confirmPassword") &&
                      "border-destructive focus-visible:ring-destructive",
                    confirmPassword.length > 0 &&
                      !passwordsMatch &&
                      "border-destructive focus-visible:ring-destructive"
                  )}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  disabled={isPending}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showConfirmPassword ? "Hide password" : "Show password"}
                  </span>
                </Button>
              </div>

              {/* Password match indicator */}
              {confirmPassword.length > 0 && (
                <div
                  className={cn(
                    "flex items-center gap-1 text-xs",
                    passwordsMatch ? "text-green-600" : "text-destructive"
                  )}
                >
                  {passwordsMatch ? (
                    <Check className="h-3 w-3" />
                  ) : (
                    <X className="h-3 w-3" />
                  )}
                  {passwordsMatch ? "Passwords match" : "Passwords don't match"}
                </div>
              )}

              {getFieldError("confirmPassword") && (
                <p className="text-sm text-destructive flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {getFieldError("confirmPassword")}
                </p>
              )}
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={isPending || state.success}
            >
              {state.success ? (
                <>
                  <Check className="mr-2 h-4 w-4" />
                  Account created! Redirecting...
                </>
              ) : isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating account...
                </>
              ) : (
                "Create Account"
              )}
            </Button>
          </div>
        </div>
      </form>

      <div className="text-muted-foreground text-center text-xs text-balance">
        By creating an account, you agree to our{" "}
        <Link
          href="/terms"
          className="underline underline-offset-4 hover:text-primary"
        >
          Terms of Service
        </Link>{" "}
        and{" "}
        <Link
          href="/privacy"
          className="underline underline-offset-4 hover:text-primary"
        >
          Privacy Policy
        </Link>
        .
      </div>
    </div>
  );
}
