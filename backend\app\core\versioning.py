"""API versioning utilities and middleware."""
import re
from typing import Optional, Dict, Any, List
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import settings
from app.core.logging import get_logger
from app.schemas.response import APIResponse

logger = get_logger(__name__)


class APIVersion:
    """API version representation."""
    
    def __init__(self, major: int, minor: int = 0, patch: int = 0):
        self.major = major
        self.minor = minor
        self.patch = patch
    
    def __str__(self) -> str:
        return f"v{self.major}.{self.minor}.{self.patch}"
    
    def __repr__(self) -> str:
        return f"APIVersion({self.major}, {self.minor}, {self.patch})"
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, APIVersion):
            return False
        return (self.major, self.minor, self.patch) == (other.major, other.minor, other.patch)
    
    def __lt__(self, other) -> bool:
        if not isinstance(other, APIVersion):
            return NotImplemented
        return (self.major, self.minor, self.patch) < (other.major, other.minor, other.patch)
    
    def __le__(self, other) -> bool:
        return self == other or self < other
    
    def __gt__(self, other) -> bool:
        return not self <= other
    
    def __ge__(self, other) -> bool:
        return not self < other
    
    @classmethod
    def from_string(cls, version_str: str) -> "APIVersion":
        """Parse version string like 'v1.2.3' or '1.2.3'."""
        # Remove 'v' prefix if present
        version_str = version_str.lstrip('v')
        
        # Parse version components
        parts = version_str.split('.')
        if len(parts) < 1 or len(parts) > 3:
            raise ValueError(f"Invalid version format: {version_str}")
        
        try:
            major = int(parts[0])
            minor = int(parts[1]) if len(parts) > 1 else 0
            patch = int(parts[2]) if len(parts) > 2 else 0
            return cls(major, minor, patch)
        except ValueError:
            raise ValueError(f"Invalid version format: {version_str}")
    
    def is_compatible_with(self, other: "APIVersion") -> bool:
        """Check if this version is compatible with another version."""
        # Same major version is compatible
        return self.major == other.major
    
    def to_dict(self) -> Dict[str, int]:
        """Convert to dictionary representation."""
        return {
            "major": self.major,
            "minor": self.minor,
            "patch": self.patch
        }


class APIVersionManager:
    """Manages API versions and compatibility."""
    
    def __init__(self):
        self.supported_versions: List[APIVersion] = [
            APIVersion(1, 0, 0),  # Current version
        ]
        self.default_version = APIVersion(1, 0, 0)
        self.deprecated_versions: List[APIVersion] = []
    
    def add_version(self, version: APIVersion, is_default: bool = False):
        """Add a supported version."""
        if version not in self.supported_versions:
            self.supported_versions.append(version)
            self.supported_versions.sort(reverse=True)  # Latest first
        
        if is_default:
            self.default_version = version
    
    def deprecate_version(self, version: APIVersion):
        """Mark a version as deprecated."""
        if version not in self.deprecated_versions:
            self.deprecated_versions.append(version)
    
    def is_supported(self, version: APIVersion) -> bool:
        """Check if version is supported."""
        return version in self.supported_versions
    
    def is_deprecated(self, version: APIVersion) -> bool:
        """Check if version is deprecated."""
        return version in self.deprecated_versions
    
    def get_latest_version(self) -> APIVersion:
        """Get the latest supported version."""
        return max(self.supported_versions) if self.supported_versions else self.default_version
    
    def get_compatible_version(self, requested: APIVersion) -> Optional[APIVersion]:
        """Get a compatible version for the requested version."""
        # Find the latest compatible version
        compatible_versions = [
            v for v in self.supported_versions
            if v.is_compatible_with(requested) and v <= requested
        ]
        
        return max(compatible_versions) if compatible_versions else None
    
    def get_version_info(self) -> Dict[str, Any]:
        """Get comprehensive version information."""
        return {
            "supported_versions": [str(v) for v in self.supported_versions],
            "default_version": str(self.default_version),
            "latest_version": str(self.get_latest_version()),
            "deprecated_versions": [str(v) for v in self.deprecated_versions],
        }


# Global version manager
version_manager = APIVersionManager()


class APIVersionMiddleware(BaseHTTPMiddleware):
    """Middleware to handle API versioning."""
    
    def __init__(self, app, version_manager: APIVersionManager):
        super().__init__(app)
        self.version_manager = version_manager
    
    async def dispatch(self, request: Request, call_next):
        """Process request with version handling."""
        try:
            # Extract version from request
            version = self._extract_version(request)
            
            # Validate version
            if version and not self.version_manager.is_supported(version):
                # Try to find compatible version
                compatible_version = self.version_manager.get_compatible_version(version)
                if not compatible_version:
                    return JSONResponse(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        content=APIResponse.error(
                            message=f"API version {version} is not supported",
                            error_code="UNSUPPORTED_API_VERSION"
                        )
                    )
                version = compatible_version
            
            # Use default version if none specified
            if not version:
                version = self.version_manager.default_version
            
            # Add version to request state
            request.state.api_version = version
            
            # Check for deprecated version
            if self.version_manager.is_deprecated(version):
                logger.warning(f"Deprecated API version used: {version}")
            
            # Process request
            response = await call_next(request)
            
            # Add version headers to response
            response.headers["X-API-Version"] = str(version)
            response.headers["X-API-Latest-Version"] = str(self.version_manager.get_latest_version())
            
            if self.version_manager.is_deprecated(version):
                response.headers["X-API-Deprecated"] = "true"
                response.headers["X-API-Sunset"] = "2024-12-31"  # Example sunset date
            
            return response
            
        except Exception as e:
            logger.error(f"API versioning middleware error: {e}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content=APIResponse.error(
                    message="Internal server error in version handling",
                    error_code="VERSION_MIDDLEWARE_ERROR"
                )
            )
    
    def _extract_version(self, request: Request) -> Optional[APIVersion]:
        """Extract API version from request."""
        # Method 1: Header-based versioning
        version_header = request.headers.get("X-API-Version") or request.headers.get("Accept-Version")
        if version_header:
            try:
                return APIVersion.from_string(version_header)
            except ValueError:
                logger.warning(f"Invalid version in header: {version_header}")
        
        # Method 2: URL path versioning (e.g., /api/v1/...)
        path = request.url.path
        version_match = re.search(r'/api/v(\d+)(?:\.(\d+))?(?:\.(\d+))?/', path)
        if version_match:
            major = int(version_match.group(1))
            minor = int(version_match.group(2)) if version_match.group(2) else 0
            patch = int(version_match.group(3)) if version_match.group(3) else 0
            return APIVersion(major, minor, patch)
        
        # Method 3: Query parameter versioning
        version_param = request.query_params.get("version") or request.query_params.get("v")
        if version_param:
            try:
                return APIVersion.from_string(version_param)
            except ValueError:
                logger.warning(f"Invalid version in query param: {version_param}")
        
        return None


def get_api_version(request: Request) -> APIVersion:
    """Get API version from request state."""
    return getattr(request.state, "api_version", version_manager.default_version)


def require_version(min_version: str):
    """Decorator to require minimum API version."""
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            current_version = get_api_version(request)
            required_version = APIVersion.from_string(min_version)
            
            if current_version < required_version:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"This endpoint requires API version {required_version} or higher. Current version: {current_version}"
                )
            
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator


def version_deprecated(deprecated_in: str, sunset_date: Optional[str] = None):
    """Decorator to mark endpoint as deprecated in specific version."""
    def decorator(func):
        async def wrapper(request: Request, *args, **kwargs):
            current_version = get_api_version(request)
            deprecated_version = APIVersion.from_string(deprecated_in)
            
            if current_version >= deprecated_version:
                logger.warning(f"Deprecated endpoint accessed: {request.url.path}")
                # You could add deprecation headers here
            
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator


class VersionedResponse:
    """Utility for creating version-specific responses."""
    
    @staticmethod
    def create_response(data: Any, version: APIVersion) -> Dict[str, Any]:
        """Create version-specific response format."""
        base_response = {
            "success": True,
            "data": data,
            "version": str(version),
            "timestamp": "2024-01-01T00:00:00Z"  # You'd use actual timestamp
        }
        
        # Version-specific modifications
        if version.major >= 2:
            # Future version enhancements
            base_response["meta"] = {
                "api_version": str(version),
                "response_format": "v2"
            }
        
        return base_response


# Version compatibility helpers
def is_version_compatible(client_version: str, server_version: str) -> bool:
    """Check if client version is compatible with server version."""
    try:
        client = APIVersion.from_string(client_version)
        server = APIVersion.from_string(server_version)
        return client.is_compatible_with(server)
    except ValueError:
        return False


def get_version_migration_info(from_version: str, to_version: str) -> Dict[str, Any]:
    """Get migration information between versions."""
    return {
        "from_version": from_version,
        "to_version": to_version,
        "breaking_changes": [],
        "new_features": [],
        "deprecated_features": [],
        "migration_guide_url": f"https://docs.example.com/migration/{from_version}-to-{to_version}"
    }
