"""Test user management endpoints."""
import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.models.user import User
from app.crud.user import user_crud
from app.schemas.user import UserCreate


class TestUserProfile:
    """Test user profile operations."""
    
    def test_get_current_user(self, client: TestClient, auth_headers: dict, test_user: User):
        """Test getting current user information."""
        response = client.get(f"{settings.API_V1_STR}/users/me", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == test_user.email
        assert data["full_name"] == test_user.full_name
        assert data["id"] == str(test_user.id)
    
    def test_get_current_user_unauthorized(self, client: TestClient):
        """Test getting current user without authentication."""
        response = client.get(f"{settings.API_V1_STR}/users/me")
        
        assert response.status_code == 401
    
    def test_update_current_user(self, client: TestClient, auth_headers: dict):
        """Test updating current user information."""
        update_data = {
            "full_name": "Updated Name",
            "bio": "Updated bio"
        }
        
        response = client.put(
            f"{settings.API_V1_STR}/users/me",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["full_name"] == update_data["full_name"]
        assert data["bio"] == update_data["bio"]


class TestUserManagement:
    """Test user management by admins."""
    
    def test_get_users_as_admin(self, client: TestClient, admin_headers: dict):
        """Test getting all users as admin."""
        response = client.get(f"{settings.API_V1_STR}/users/", headers=admin_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1  # At least the admin user
    
    def test_get_users_as_regular_user(self, client: TestClient, auth_headers: dict):
        """Test getting all users as regular user (should fail)."""
        response = client.get(f"{settings.API_V1_STR}/users/", headers=auth_headers)
        
        assert response.status_code == 403
    
    def test_get_user_by_id_own_profile(self, client: TestClient, auth_headers: dict, test_user: User):
        """Test getting own user profile by ID."""
        response = client.get(
            f"{settings.API_V1_STR}/users/{test_user.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(test_user.id)
    
    def test_get_user_by_id_other_user(self, client: TestClient, auth_headers: dict, admin_user: User):
        """Test getting other user's profile (should fail for regular user)."""
        response = client.get(
            f"{settings.API_V1_STR}/users/{admin_user.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 403
    
    def test_get_user_by_id_as_admin(self, client: TestClient, admin_headers: dict, test_user: User):
        """Test getting any user's profile as admin."""
        response = client.get(
            f"{settings.API_V1_STR}/users/{test_user.id}",
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(test_user.id)
    
    def test_update_user_as_admin(self, client: TestClient, admin_headers: dict, test_user: User):
        """Test updating user as admin."""
        update_data = {
            "full_name": "Admin Updated Name",
            "role": "moderator"
        }
        
        response = client.put(
            f"{settings.API_V1_STR}/users/{test_user.id}",
            json=update_data,
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["full_name"] == update_data["full_name"]
        assert data["role"] == update_data["role"]
    
    def test_update_user_as_regular_user(self, client: TestClient, auth_headers: dict, admin_user: User):
        """Test updating other user as regular user (should fail)."""
        update_data = {
            "full_name": "Unauthorized Update"
        }
        
        response = client.put(
            f"{settings.API_V1_STR}/users/{admin_user.id}",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 403
    
    @pytest.mark.asyncio
    async def test_delete_user_as_admin(self, client: TestClient, admin_headers: dict, async_db: AsyncSession):
        """Test deleting user as admin."""
        # Create a user to delete
        user_data = UserCreate(
            email="<EMAIL>",
            password="password123",
            full_name="To Delete"
        )
        user = await user_crud.create_async(async_db, obj_in=user_data)
        
        response = client.delete(
            f"{settings.API_V1_STR}/users/{user.id}",
            headers=admin_headers
        )
        
        assert response.status_code == 200
        assert "User deleted successfully" in response.json()["message"]
    
    def test_delete_user_as_regular_user(self, client: TestClient, auth_headers: dict, admin_user: User):
        """Test deleting user as regular user (should fail)."""
        response = client.delete(
            f"{settings.API_V1_STR}/users/{admin_user.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 403
    
    def test_delete_self_as_admin(self, client: TestClient, admin_headers: dict, admin_user: User):
        """Test admin trying to delete their own account (should fail)."""
        response = client.delete(
            f"{settings.API_V1_STR}/users/{admin_user.id}",
            headers=admin_headers
        )
        
        assert response.status_code == 400
        assert "Cannot delete your own account" in response.json()["detail"]
    
    def test_get_nonexistent_user(self, client: TestClient, admin_headers: dict):
        """Test getting nonexistent user."""
        fake_uuid = "********-0000-0000-0000-************"
        response = client.get(
            f"{settings.API_V1_STR}/users/{fake_uuid}",
            headers=admin_headers
        )
        
        assert response.status_code == 404
        assert "User not found" in response.json()["detail"]


class TestUserPagination:
    """Test user listing pagination."""
    
    @pytest.mark.asyncio
    async def test_users_pagination(self, client: TestClient, admin_headers: dict, async_db: AsyncSession):
        """Test user listing with pagination."""
        # Create multiple users
        for i in range(5):
            user_data = UserCreate(
                email=f"user{i}@example.com",
                password="password123",
                full_name=f"User {i}"
            )
            await user_crud.create_async(async_db, obj_in=user_data)
        
        # Test pagination
        response = client.get(
            f"{settings.API_V1_STR}/users/?skip=0&limit=3",
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) <= 3
        
        # Test second page
        response = client.get(
            f"{settings.API_V1_STR}/users/?skip=3&limit=3",
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)


class TestUserValidation:
    """Test user data validation."""
    
    def test_update_user_invalid_email(self, client: TestClient, auth_headers: dict):
        """Test updating user with invalid email."""
        update_data = {
            "email": "invalid-email"
        }
        
        response = client.put(
            f"{settings.API_V1_STR}/users/me",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422
    
    def test_update_user_empty_name(self, client: TestClient, auth_headers: dict):
        """Test updating user with empty name."""
        update_data = {
            "full_name": ""
        }
        
        response = client.put(
            f"{settings.API_V1_STR}/users/me",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422
