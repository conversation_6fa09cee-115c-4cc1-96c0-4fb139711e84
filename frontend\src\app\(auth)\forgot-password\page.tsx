"use client";

import { useActionState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { ArrowLeft, Mail, Loader2, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { forgotPasswordAction } from "@/lib/actions/auth-actions";

export default function ForgotPasswordPage() {
  const router = useRouter();
  const [state, formAction, isPending] = useActionState(forgotPasswordAction, {
    success: false,
    message: "",
    errors: {},
  });

  // Handle success state
  useEffect(() => {
    if (state.success) {
      toast.success("Password reset email sent!", {
        description: state.message || "Please check your email for reset instructions.",
        duration: 5000,
      });
    }
  }, [state.success, state.message]);

  // Handle errors
  useEffect(() => {
    if (state.message && !state.success) {
      toast.error("Request failed", {
        description: state.message,
        duration: 5000,
      });
    }
  }, [state.message, state.success]);

  const getFieldError = (fieldName: string) => {
    if (state.errors && Array.isArray(state.errors)) {
      const fieldError = state.errors.find((error: any) => error.path?.[0] === fieldName);
      if (fieldError) {
        return fieldError.message;
      }
    }
    return undefined;
  };

  const handleBackToLogin = () => {
    router.push("/login");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {state.success ? (
              <CheckCircle className="h-12 w-12 text-green-500" />
            ) : (
              <Mail className="h-12 w-12 text-blue-500" />
            )}
          </div>
          <CardTitle className="text-2xl font-bold">
            {state.success ? "Email Sent!" : "Forgot Password?"}
          </CardTitle>
          <CardDescription className="text-center">
            {state.success
              ? "We've sent password reset instructions to your email address."
              : "Enter your email address and we'll send you a link to reset your password."}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {!state.success ? (
            <form action={formAction} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email address"
                  required
                  disabled={isPending}
                  className={getFieldError("email") ? "border-red-500" : ""}
                />
                {getFieldError("email") && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <span className="text-red-500">•</span>
                    {getFieldError("email")}
                  </p>
                )}
              </div>

              <Button 
                type="submit" 
                className="w-full" 
                disabled={isPending}
              >
                {isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sending Reset Link...
                  </>
                ) : (
                  <>
                    <Mail className="mr-2 h-4 w-4" />
                    Send Reset Link
                  </>
                )}
              </Button>
            </form>
          ) : (
            <div className="space-y-4 text-center">
              <p className="text-sm text-muted-foreground">
                If an account with that email exists, you'll receive password reset instructions shortly.
              </p>
              <p className="text-sm text-muted-foreground">
                Didn't receive the email? Check your spam folder or try again.
              </p>
            </div>
          )}

          <div className="pt-4 border-t">
            <Button 
              variant="ghost" 
              onClick={handleBackToLogin}
              className="w-full"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Sign In
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
