"""Test authentication endpoints."""
import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.models.user import User
from app.crud.user import user_crud
from app.crud.session import session_crud
from app.crud.email_verification import email_verification_crud


class TestAuthRegistration:
    """Test user registration."""
    
    def test_register_new_user(self, client: TestClient):
        """Test successful user registration."""
        user_data = {
            "email": "<EMAIL>",
            "password": "newpassword123",
            "full_name": "New User"
        }
        
        response = client.post(f"{settings.API_V1_STR}/auth/register", json=user_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == user_data["email"]
        assert data["full_name"] == user_data["full_name"]
        assert data["is_active"] is True
        assert data["is_verified"] is False
        assert "id" in data
    
    def test_register_duplicate_email(self, client: TestClient, test_user: User):
        """Test registration with existing email."""
        user_data = {
            "email": test_user.email,
            "password": "newpassword123",
            "full_name": "Duplicate User"
        }
        
        response = client.post(f"{settings.API_V1_STR}/auth/register", json=user_data)
        
        assert response.status_code == 400
        assert "Email already registered" in response.json()["detail"]
    
    def test_register_invalid_email(self, client: TestClient):
        """Test registration with invalid email."""
        user_data = {
            "email": "invalid-email",
            "password": "password123",
            "full_name": "Test User"
        }
        
        response = client.post(f"{settings.API_V1_STR}/auth/register", json=user_data)
        
        assert response.status_code == 422
    
    def test_register_weak_password(self, client: TestClient):
        """Test registration with weak password."""
        user_data = {
            "email": "<EMAIL>",
            "password": "123",  # Too short
            "full_name": "Test User"
        }
        
        response = client.post(f"{settings.API_V1_STR}/auth/register", json=user_data)
        
        assert response.status_code == 422


class TestAuthLogin:
    """Test user login."""
    
    def test_login_valid_credentials(self, client: TestClient, test_user: User):
        """Test successful login."""
        login_data = {
            "username": test_user.email,
            "password": "testpassword123"
        }
        
        response = client.post(f"{settings.API_V1_STR}/auth/login", data=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
        
        # Check session cookie is set
        assert "session_token" in response.cookies
    
    def test_login_invalid_email(self, client: TestClient):
        """Test login with invalid email."""
        login_data = {
            "username": "<EMAIL>",
            "password": "password123"
        }
        
        response = client.post(f"{settings.API_V1_STR}/auth/login", data=login_data)
        
        assert response.status_code == 401
        assert "Incorrect email or password" in response.json()["detail"]
    
    def test_login_invalid_password(self, client: TestClient, test_user: User):
        """Test login with invalid password."""
        login_data = {
            "username": test_user.email,
            "password": "wrongpassword"
        }
        
        response = client.post(f"{settings.API_V1_STR}/auth/login", data=login_data)
        
        assert response.status_code == 401
        assert "Incorrect email or password" in response.json()["detail"]
    
    async def test_login_inactive_user(self, client: TestClient, async_db: AsyncSession):
        """Test login with inactive user."""
        # Create inactive user
        from app.schemas.user import UserCreate
        user_data = UserCreate(
            email="<EMAIL>",
            password="password123",
            full_name="Inactive User"
        )
        user = await user_crud.create_async(async_db, obj_in=user_data)
        user.is_active = False
        await async_db.commit()
        
        login_data = {
            "username": user.email,
            "password": "password123"
        }
        
        response = client.post(f"{settings.API_V1_STR}/auth/login", data=login_data)
        
        assert response.status_code == 400
        assert "Inactive user" in response.json()["detail"]


class TestAuthTokens:
    """Test token operations."""
    
    def test_refresh_token(self, client: TestClient, test_user: User):
        """Test token refresh."""
        # First login to get tokens
        login_data = {
            "username": test_user.email,
            "password": "testpassword123"
        }
        
        login_response = client.post(f"{settings.API_V1_STR}/auth/login", data=login_data)
        refresh_token = login_response.json()["refresh_token"]
        
        # Refresh token
        response = client.post(
            f"{settings.API_V1_STR}/auth/refresh",
            json={"refresh_token": refresh_token}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["refresh_token"] == refresh_token  # Same refresh token
        assert data["token_type"] == "bearer"
    
    def test_refresh_invalid_token(self, client: TestClient):
        """Test refresh with invalid token."""
        response = client.post(
            f"{settings.API_V1_STR}/auth/refresh",
            json={"refresh_token": "invalid_token"}
        )
        
        assert response.status_code == 401
        assert "Invalid refresh token" in response.json()["detail"]


class TestAuthLogout:
    """Test logout operations."""
    
    def test_logout(self, client: TestClient, auth_headers: dict):
        """Test successful logout."""
        response = client.post(f"{settings.API_V1_STR}/auth/logout", headers=auth_headers)
        
        assert response.status_code == 200
        assert "Successfully logged out" in response.json()["message"]
    
    def test_logout_all(self, client: TestClient, auth_headers: dict):
        """Test logout from all devices."""
        response = client.post(f"{settings.API_V1_STR}/auth/logout-all", headers=auth_headers)
        
        assert response.status_code == 200
        assert "Logged out from" in response.json()["message"]


class TestEmailVerification:
    """Test email verification."""
    
    @pytest.mark.asyncio
    async def test_verify_email_valid_token(self, client: TestClient, async_db: AsyncSession, test_user: User):
        """Test email verification with valid token."""
        # Create verification token
        verification = await email_verification_crud.create_verification(
            async_db,
            user_id=str(test_user.id),
            email=test_user.email
        )
        
        response = client.post(
            f"{settings.API_V1_STR}/auth/verify-email",
            params={"token": verification.token}
        )
        
        assert response.status_code == 200
        assert "Email verified successfully" in response.json()["message"]
    
    def test_verify_email_invalid_token(self, client: TestClient):
        """Test email verification with invalid token."""
        response = client.post(
            f"{settings.API_V1_STR}/auth/verify-email",
            params={"token": "invalid_token"}
        )
        
        assert response.status_code == 400
        assert "Invalid or expired verification token" in response.json()["detail"]
    
    def test_resend_verification(self, client: TestClient, test_user: User):
        """Test resending verification email."""
        response = client.post(
            f"{settings.API_V1_STR}/auth/resend-verification",
            params={"email": test_user.email}
        )
        
        assert response.status_code == 200
        assert "verification link has been sent" in response.json()["message"]
    
    def test_resend_verification_nonexistent_email(self, client: TestClient):
        """Test resending verification for nonexistent email."""
        response = client.post(
            f"{settings.API_V1_STR}/auth/resend-verification",
            params={"email": "<EMAIL>"}
        )
        
        assert response.status_code == 200
        # Should not reveal if email exists
        assert "verification link has been sent" in response.json()["message"]
