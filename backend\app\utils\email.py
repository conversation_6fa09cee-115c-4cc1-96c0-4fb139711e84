"""Email utilities using Resend."""

import resend
import logging
from typing import Optional, Dict, Any
from datetime import datetime

from app.core.config import settings
from app.core.constants import (
    EmailTemplate,
    EMAIL_VERIFICATION_SUBJECT,
    PASSWORD_RESET_SUBJECT,
    WELCOME_SUBJECT,
)

logger = logging.getLogger(__name__)


class EmailService:
    """Email service using Resend API."""

    def __init__(self):
        if settings.RESEND_API_KEY:
            resend.api_key = settings.RESEND_API_KEY

    def _get_base_url(self) -> str:
        """Get base URL for email links."""
        if settings.ENVIRONMENT == "production":
            return "https://yourdomain.com"  # Replace with actual domain
        return "http://localhost:3002"

    def _send_email(
        self, to: str, subject: str, html: str, text: Optional[str] = None
    ) -> bool:
        """Send email using Resend API."""
        if not settings.RESEND_API_KEY:
            logger.info(
                f"Email service not configured. Would send email to {to} with subject: {subject}"
            )
            return True

        try:
            params = {
                "from": settings.FROM_EMAIL,
                "to": [to],
                "subject": subject,
                "html": html,
            }

            if text:
                params["text"] = text

            response = resend.Emails.send(params)
            logger.info(f"Email sent successfully to {to}. Response: {response}")
            return True

        except Exception as e:
            logger.error(f"Failed to send email to {to}: {str(e)}")
            return False

    def send_verification_email(
        self, email: str, verification_token: str, user_name: Optional[str] = None
    ) -> bool:
        """Send email verification email."""
        base_url = self._get_base_url()
        verification_url = f"{base_url}/verify-email?token={verification_token}"

        display_name = user_name or email.split("@")[0]

        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Verify Your Email</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #f8f9fa; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .button {{
                    display: inline-block;
                    padding: 12px 24px;
                    background-color: #007bff;
                    color: white;
                    text-decoration: none;
                    border-radius: 4px;
                    margin: 20px 0;
                }}
                .footer {{ background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>{settings.PROJECT_NAME}</h1>
                </div>
                <div class="content">
                    <h2>Welcome, {display_name}!</h2>
                    <p>Thank you for signing up. Please verify your email address to complete your registration.</p>
                    <p>Click the button below to verify your email:</p>
                    <a href="{verification_url}" class="button">Verify Email Address</a>
                    <p>Or copy and paste this link into your browser:</p>
                    <p><a href="{verification_url}">{verification_url}</a></p>
                    <p>This link will expire in {settings.EMAIL_VERIFICATION_EXPIRE_HOURS} hours.</p>
                    <p>If you didn't create an account, you can safely ignore this email.</p>
                </div>
                <div class="footer">
                    <p>&copy; {datetime.now().year} {settings.PROJECT_NAME}. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """

        text = f"""
        Welcome to {settings.PROJECT_NAME}!

        Please verify your email address by clicking the link below:
        {verification_url}

        This link will expire in {settings.EMAIL_VERIFICATION_EXPIRE_HOURS} hours.

        If you didn't create an account, you can safely ignore this email.
        """

        return self._send_email(email, EMAIL_VERIFICATION_SUBJECT, html, text)

    def send_password_reset_email(
        self, email: str, reset_token: str, user_name: Optional[str] = None
    ) -> bool:
        """Send password reset email."""
        base_url = self._get_base_url()
        reset_url = f"{base_url}/reset-password?token={reset_token}"

        display_name = user_name or email.split("@")[0]

        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Reset Your Password</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #f8f9fa; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .button {{
                    display: inline-block;
                    padding: 12px 24px;
                    background-color: #dc3545;
                    color: white;
                    text-decoration: none;
                    border-radius: 4px;
                    margin: 20px 0;
                }}
                .footer {{ background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>{settings.PROJECT_NAME}</h1>
                </div>
                <div class="content">
                    <h2>Password Reset Request</h2>
                    <p>Hello {display_name},</p>
                    <p>We received a request to reset your password. Click the button below to create a new password:</p>
                    <a href="{reset_url}" class="button">Reset Password</a>
                    <p>Or copy and paste this link into your browser:</p>
                    <p><a href="{reset_url}">{reset_url}</a></p>
                    <p>This link will expire in {settings.PASSWORD_RESET_EXPIRE_HOURS} hour(s).</p>
                    <p>If you didn't request a password reset, you can safely ignore this email.</p>
                </div>
                <div class="footer">
                    <p>&copy; {datetime.now().year} {settings.PROJECT_NAME}. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """

        text = f"""
        Password Reset Request

        Hello {display_name},

        We received a request to reset your password. Click the link below to create a new password:
        {reset_url}

        This link will expire in {settings.PASSWORD_RESET_EXPIRE_HOURS} hour(s).

        If you didn't request a password reset, you can safely ignore this email.
        """

        return self._send_email(email, PASSWORD_RESET_SUBJECT, html, text)

    def send_welcome_email(self, email: str, user_name: Optional[str] = None) -> bool:
        """Send welcome email after successful verification."""
        display_name = user_name or email.split("@")[0]

        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Welcome!</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #f8f9fa; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .footer {{ background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>{settings.PROJECT_NAME}</h1>
                </div>
                <div class="content">
                    <h2>Welcome to {settings.PROJECT_NAME}!</h2>
                    <p>Hello {display_name},</p>
                    <p>Your email has been successfully verified and your account is now active.</p>
                    <p>You can now enjoy all the features of our platform.</p>
                    <p>If you have any questions, feel free to contact our support team.</p>
                </div>
                <div class="footer">
                    <p>&copy; {datetime.now().year} {settings.PROJECT_NAME}. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """

        return self._send_email(email, WELCOME_SUBJECT, html)


# Global email service instance
email_service = EmailService()


# Legacy functions for backward compatibility
def send_verification_email(email: str, verification_token: str) -> bool:
    """Send email verification email (legacy function)."""
    return email_service.send_verification_email(email, verification_token)


def send_password_reset_email(email: str, reset_token: str) -> bool:
    """Send password reset email (legacy function)."""
    return email_service.send_password_reset_email(email, reset_token)
