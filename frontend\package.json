{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:all": "npm run test && npm run test:e2e", "generate:types": "openapi-typescript http://localhost:8000/api/v1/openapi.json -o src/types/api.d.ts", "generate:types:check": "openapi-typescript http://localhost:8000/api/v1/openapi.json -o src/types/api.d.ts --check", "check:types": "node scripts/check-types.js"}, "dependencies": {"@apidevtools/swagger-parser": "^12.0.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.536.0", "next": "15.4.5", "openapi-typescript": "^7.8.0", "react": "19.1.0", "react-dom": "19.1.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@hookform/resolvers": "^5.2.1", "@next/bundle-analyzer": "^15.4.5", "@playwright/test": "^1.54.2", "@tailwindcss/postcss": "^4", "@tanstack/react-query": "^5.84.1", "@tanstack/react-query-devtools": "^5.84.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "axios": "^1.11.0", "cross-env": "^10.0.0", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "eslint": "^9.32.0", "eslint-config-next": "^15.4.5", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "js-cookie": "^3.0.5", "next-themes": "^0.4.6", "prettier": "^3.6.2", "react-hook-form": "^7.62.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5", "zod": "^4.0.14", "zustand": "^5.0.7"}}