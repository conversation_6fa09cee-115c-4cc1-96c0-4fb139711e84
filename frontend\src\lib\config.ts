// Centralized configuration management
import { z } from "zod";

// Environment configuration schema
const configSchema = z.object({
  // API Configuration
  API_BASE_URL: z.string().url().default("http://localhost:8000"),
  API_VERSION: z.string().default("v1"),

  // Authentication
  AUTH_COOKIE_NAME: z.string().default("session_token"),
  TOKEN_STORAGE_KEY: z.string().default("auth_tokens"),

  // Application
  APP_NAME: z.string().default("WebApp"),
  APP_DESCRIPTION: z.string().default("A modern web application"),

  // Environment
  NODE_ENV: z.enum(["development", "production", "test"]).default("development"),

  // Features
  ENABLE_EMAIL_VERIFICATION: z.boolean().default(true),
  ENABLE_SOCIAL_AUTH: z.boolean().default(false),
});

// Parse and validate environment variables
const parseConfig = () => {
  const env = {
    API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
    API_VERSION: process.env.NEXT_PUBLIC_API_VERSION,
    AUTH_COOKIE_NAME: process.env.NEXT_PUBLIC_AUTH_COOKIE_NAME,
    TOKEN_STORAGE_KEY: process.env.NEXT_PUBLIC_TOKEN_STORAGE_KEY,
    APP_NAME: process.env.NEXT_PUBLIC_APP_NAME,
    APP_DESCRIPTION: process.env.NEXT_PUBLIC_APP_DESCRIPTION,
    NODE_ENV: process.env.NODE_ENV,
    ENABLE_EMAIL_VERIFICATION: process.env.NEXT_PUBLIC_ENABLE_EMAIL_VERIFICATION === "true",
    ENABLE_SOCIAL_AUTH: process.env.NEXT_PUBLIC_ENABLE_SOCIAL_AUTH === "true",
  };

  try {
    return configSchema.parse(env);
  } catch (error) {
    console.error("Invalid configuration:", error);
    throw new Error("Configuration validation failed");
  }
};

// Export validated configuration
export const config = parseConfig();

// API endpoints builder
export const apiEndpoints = {
  auth: {
    login: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/login`,
    register: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/register`,
    logout: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/logout`,
    refresh: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/refresh`,
    verify: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/verify`,
    resendVerification: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/resend-verification`,
    forgotPassword: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/forgot-password`,
    resetPassword: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/reset-password`,
    me: `${config.API_BASE_URL}/api/${config.API_VERSION}/auth/me`,
  },
  users: {
    profile: `${config.API_BASE_URL}/api/${config.API_VERSION}/users/me`,
    update: `${config.API_BASE_URL}/api/${config.API_VERSION}/users/me`,
  },
} as const;

// Type for configuration
export type Config = z.infer<typeof configSchema>;
