"""FastAPI application with authentication, authorization, and Next.js compatibility."""
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.gzip import GZipMiddleware

from app.core.config import settings
from app.core.middleware import setup_middleware
from app.core.versioning import APIVersionMiddleware, version_manager
from app.core.cache import cache_maintenance_task, CacheManager, cache
from app.core.exceptions import setup_exception_handlers
from app.core.logging import setup_logging, get_logger
from app.api.main import api_router
from app.database.session import async_engine
from app.database.base import Base

# Setup logging
setup_logging()
logger = get_logger("main")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events."""
    # Startup
    logger.info("Starting up FastAPI application...")

    # Create database tables (in production, use Alembic migrations)
    if settings.ENVIRONMENT == "development":
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

    # Warm up cache
    await CacheManager.warm_cache()

    # Start background tasks
    import asyncio
    asyncio.create_task(cache_maintenance_task())

    logger.info("FastAPI application started successfully")

    yield

    # Shutdown
    logger.info("Shutting down FastAPI application...")

    # Close Redis connection
    await cache.close()

    # Close database connections
    await async_engine.dispose()
    logger.info("FastAPI application shut down successfully")


# Create FastAPI application
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.DESCRIPTION,
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json" if settings.DEBUG else None,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan,
    contact={
        "name": "API Support",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT",
    },
    openapi_tags=[
        {
            "name": "Authentication",
            "description": "User authentication and authorization operations",
        },
        {
            "name": "User Management",
            "description": "User profile and account management operations",
        },
        {
            "name": "Health",
            "description": "Application health and status endpoints",
        },
    ],
)

# Setup middleware
setup_middleware(app)

# Add API versioning middleware
app.add_middleware(APIVersionMiddleware, version_manager=version_manager)

# Setup exception handlers
setup_exception_handlers(app)

# Add GZip middleware for response compression
app.add_middleware(GZipMiddleware, minimum_size=1000)

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)


# Health check endpoint
@app.get(
    "/health",
    tags=["Health"],
    summary="Health check",
    description="Check the health status of the application"
)
async def health_check():
    """
    Health check endpoint.

    Returns the current status of the application including:
    - Service status
    - Version information
    - Environment details
    """
    return {
        "status": "healthy",
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
    }


# Root endpoint
@app.get(
    "/",
    tags=["Health"],
    summary="API information",
    description="Get basic information about the API"
)
async def root():
    """
    Root endpoint with API information.

    Returns basic information about the API including:
    - Welcome message
    - Version information
    - Documentation URLs
    - API base URL
    """
    return {
        "message": f"Welcome to {settings.PROJECT_NAME}",
        "version": settings.VERSION,
        "docs_url": "/docs" if settings.DEBUG else None,
        "api_url": settings.API_V1_STR,
    }