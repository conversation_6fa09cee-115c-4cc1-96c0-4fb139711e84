"""Logging configuration for the application."""
import logging
import logging.config
import sys
from typing import Dict, Any
from pathlib import Path

from app.core.config import settings


def get_logging_config() -> Dict[str, Any]:
    """Get logging configuration based on environment."""
    
    # Create logs directory if it doesn't exist
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "console": {
                "format": "%(levelname)s: %(message)s",
            },
            "console_detailed": {
                "format": "%(asctime)s - %(levelname)s - %(message)s",
                "datefmt": "%H:%M:%S",
            },
            "default": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s:%(lineno)d - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "json": {
                "()": "pythonjsonlogger.json.JsonFormatter",
                "format": "%(asctime)s %(name)s %(levelname)s %(module)s %(funcName)s %(lineno)d %(message)s",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.LOG_LEVEL_CONSOLE,
                "formatter": "console",
                "stream": sys.stdout,
            },
            "console_startup": {
                "class": "logging.StreamHandler",
                "level": "INFO",
                "formatter": "console_detailed",
                "stream": sys.stdout,
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": settings.LOG_LEVEL_FILE,
                "formatter": "detailed",
                "filename": "logs/app.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8",
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "detailed",
                "filename": "logs/error.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf8",
            },
        },
        "loggers": {
            "app": {
                "level": "DEBUG" if settings.DEBUG else "INFO",
                "handlers": ["console", "file", "error_file"],
                "propagate": False,
            },
            "app.main": {
                "level": "INFO",
                "handlers": ["console_startup", "file", "error_file"],  # Show startup messages
                "propagate": False,
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["file"],  # Only to file, not console
                "propagate": False,
            },
            "uvicorn.error": {
                "level": "INFO",
                "handlers": ["console_startup", "file", "error_file"],  # Show startup/shutdown
                "propagate": False,
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["file"],  # Only to file, not console
                "propagate": False,
            },
            "sqlalchemy.engine": {
                "level": "WARNING",
                "handlers": ["file"],  # Only to file, not console
                "propagate": False,
            },
            "sqlalchemy.pool": {
                "level": "WARNING",
                "handlers": ["file"],  # Only to file, not console
                "propagate": False,
            },
        },
        "root": {
            "level": "INFO",
            "handlers": ["file"],  # Only to file, not console
        },
    }
    
    # Adjust for production environment
    if settings.ENVIRONMENT == "production":
        # Use JSON formatter for production
        config["handlers"]["console"]["formatter"] = "json"
        config["handlers"]["file"]["formatter"] = "json"
        config["handlers"]["error_file"]["formatter"] = "json"
        
        # Reduce console logging in production
        config["handlers"]["console"]["level"] = "WARNING"
        
        # Add security logger for production
        config["handlers"]["security_file"] = {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "INFO",
            "formatter": "json",
            "filename": "logs/security.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 10,
            "encoding": "utf8",
        }
        
        config["loggers"]["app.security"] = {
            "level": "INFO",
            "handlers": ["security_file", "error_file"],
            "propagate": False,
        }
    
    return config


def setup_logging():
    """Setup logging configuration."""
    config = get_logging_config()
    logging.config.dictConfig(config)
    
    # Set up logger for the application
    logger = logging.getLogger("app")
    logger.info(f"Logging configured for environment: {settings.ENVIRONMENT}")
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """Get a logger with the specified name."""
    return logging.getLogger(f"app.{name}")


# Security logger for authentication and authorization events
security_logger = logging.getLogger("app.security")


def log_security_event(event_type: str, user_id: str = None, details: str = None, request_ip: str = None):
    """Log security-related events."""
    extra_data = {
        "event_type": event_type,
        "user_id": user_id,
        "request_ip": request_ip,
    }
    
    message = f"Security event: {event_type}"
    if details:
        message += f" - {details}"
    
    security_logger.info(message, extra=extra_data)


def log_auth_success(user_id: str, request_ip: str = None):
    """Log successful authentication."""
    log_security_event("AUTH_SUCCESS", user_id=user_id, request_ip=request_ip)


def log_auth_failure(email: str, request_ip: str = None, reason: str = None):
    """Log failed authentication attempt."""
    details = f"Email: {email}"
    if reason:
        details += f", Reason: {reason}"
    log_security_event("AUTH_FAILURE", details=details, request_ip=request_ip)


def log_permission_denied(user_id: str, resource: str, request_ip: str = None):
    """Log permission denied events."""
    details = f"Resource: {resource}"
    log_security_event("PERMISSION_DENIED", user_id=user_id, details=details, request_ip=request_ip)


def log_rate_limit_exceeded(request_ip: str, endpoint: str):
    """Log rate limit exceeded events."""
    details = f"Endpoint: {endpoint}"
    log_security_event("RATE_LIMIT_EXCEEDED", details=details, request_ip=request_ip)


# Performance logger
performance_logger = logging.getLogger("app.performance")


def log_slow_query(query_time: float, query: str):
    """Log slow database queries."""
    if query_time > 1.0:  # Log queries taking more than 1 second
        performance_logger.warning(
            f"Slow query detected: {query_time:.2f}s - {query[:100]}...",
            extra={"query_time": query_time, "query": query}
        )


# API logger
api_logger = logging.getLogger("app.api")


def log_api_request(method: str, path: str, user_id: str = None, response_time: float = None):
    """Log API requests."""
    extra_data = {
        "method": method,
        "path": path,
        "user_id": user_id,
        "response_time": response_time,
    }
    
    message = f"{method} {path}"
    if response_time:
        message += f" - {response_time:.3f}s"
    
    api_logger.info(message, extra=extra_data)
