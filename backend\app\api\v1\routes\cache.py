"""Cache management API routes for frontend integration."""

import json
import re
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from fastapi.responses import JSONResponse

from app.api.deps import get_current_user, require_admin_permission
from app.models.user import User
from app.core.cache import cache
from app.core.config import settings
from app.core.logging import get_logger
from app.schemas.cache import (
    CacheItemRequest,
    CacheItemResponse,
    CacheKeyInfo,
    CacheStatsResponse,
    CacheHealthResponse,
    CacheBulkRequest,
    CacheBulkResponse,
    CacheSearchRequest,
    CacheSearchResponse,
    CacheInvalidationRequest,
    CacheInvalidationResponse,
    UserCacheStatsResponse,
)

router = APIRouter(prefix="/cache", tags=["Cache Management"])
logger = get_logger(__name__)


def get_user_cache_key(user_id: str, key: str) -> str:
    """Generate user-scoped cache key."""
    # Sanitize key to prevent injection
    sanitized_key = re.sub(r"[^a-zA-Z0-9_\-:.]", "_", key)
    return f"user:{user_id}:{sanitized_key}"


def validate_cache_key(key: str) -> bool:
    """Validate cache key format and security."""
    # Check length
    if len(key) > 250:
        return False

    # Check for dangerous patterns
    dangerous_patterns = ["..", "//", "\\", "*", "?"]
    return not any(pattern in key for pattern in dangerous_patterns)


@router.get(
    "/{key}",
    response_model=CacheItemResponse,
    summary="Get cached item",
    description="Retrieve a cached item for the current user",
)
async def get_cache_item(
    key: str = Path(..., description="Cache key to retrieve"),
    current_user: User = Depends(get_current_user),
) -> CacheItemResponse:
    """Get cached item for current user."""
    if not validate_cache_key(key):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid cache key format"
        )

    try:
        user_key = get_user_cache_key(current_user.id, key)
        cached_data = await cache.get(user_key)

        if cached_data is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Cache item not found"
            )

        # Get TTL if available
        ttl = await cache.ttl(user_key) if hasattr(cache, "ttl") else None

        logger.debug(f"Cache hit for user {current_user.id}, key: {key}")

        return CacheItemResponse(data=cached_data, ttl=ttl if ttl and ttl > 0 else None)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error retrieving cache item {key} for user {current_user.id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve cache item",
        )


@router.put(
    "/{key}",
    status_code=status.HTTP_201_CREATED,
    summary="Set cached item",
    description="Store an item in cache for the current user",
)
async def set_cache_item(
    key: str = Path(..., description="Cache key to set"),
    request: CacheItemRequest = ...,
    current_user: User = Depends(get_current_user),
) -> JSONResponse:
    """Set cached item for current user."""
    if not validate_cache_key(key):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid cache key format"
        )

    try:
        user_key = get_user_cache_key(current_user.id, key)

        # Validate data size (prevent abuse)
        data_size = len(json.dumps(request.data)) if request.data else 0
        if data_size > 1024 * 1024:  # 1MB limit
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="Cache item too large (max 1MB)",
            )

        await cache.set(user_key, request.data, request.ttl)

        logger.debug(
            f"Cache set for user {current_user.id}, key: {key}, TTL: {request.ttl}s"
        )

        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content={
                "message": "Cache item set successfully",
                "key": key,
                "ttl": request.ttl,
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error setting cache item {key} for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to set cache item",
        )


@router.delete(
    "/{key}",
    summary="Delete cached item",
    description="Remove a cached item for the current user",
)
async def delete_cache_item(
    key: str = Path(..., description="Cache key to delete"),
    current_user: User = Depends(get_current_user),
) -> JSONResponse:
    """Delete cached item for current user."""
    if not validate_cache_key(key):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid cache key format"
        )

    try:
        user_key = get_user_cache_key(current_user.id, key)
        deleted = await cache.delete(user_key)

        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Cache item not found"
            )

        logger.debug(f"Cache item deleted for user {current_user.id}, key: {key}")

        return JSONResponse(
            content={"message": "Cache item deleted successfully", "key": key}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting cache item {key} for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete cache item",
        )


@router.delete(
    "/clear",
    summary="Clear user cache",
    description="Clear all cached items for the current user",
)
async def clear_user_cache(
    current_user: User = Depends(get_current_user),
    confirm: bool = Query(
        False, description="Confirmation flag for destructive operation"
    ),
) -> JSONResponse:
    """Clear all cache items for current user."""
    if not confirm:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Must set confirm=true to clear cache",
        )

    try:
        pattern = f"user:{current_user.id}:*"
        cleared_count = await cache.delete_pattern(pattern)

        logger.info(f"Cleared {cleared_count} cache items for user {current_user.id}")

        return JSONResponse(
            content={
                "message": "User cache cleared successfully",
                "cleared_count": cleared_count,
            }
        )

    except Exception as e:
        logger.error(f"Error clearing cache for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear user cache",
        )


@router.post(
    "/bulk",
    response_model=CacheBulkResponse,
    summary="Bulk cache operations",
    description="Set multiple cache items in a single request",
)
async def bulk_cache_operations(
    request: CacheBulkRequest, current_user: User = Depends(get_current_user)
) -> CacheBulkResponse:
    """Perform bulk cache operations for current user."""
    success_count = 0
    failed_count = 0
    failed_keys = []
    errors = {}

    for key, cache_request in request.items.items():
        try:
            if not validate_cache_key(key):
                failed_count += 1
                failed_keys.append(key)
                errors[key] = "Invalid cache key format"
                continue

            user_key = get_user_cache_key(current_user.id, key)

            # Validate data size
            data_size = len(json.dumps(cache_request.data)) if cache_request.data else 0
            if data_size > 1024 * 1024:  # 1MB limit
                failed_count += 1
                failed_keys.append(key)
                errors[key] = "Cache item too large (max 1MB)"
                continue

            await cache.set(user_key, cache_request.data, cache_request.ttl)
            success_count += 1

        except Exception as e:
            failed_count += 1
            failed_keys.append(key)
            errors[key] = str(e)
            logger.error(f"Bulk cache error for key {key}, user {current_user.id}: {e}")

    logger.debug(
        f"Bulk cache operation for user {current_user.id}: {success_count} success, {failed_count} failed"
    )

    return CacheBulkResponse(
        success_count=success_count,
        failed_count=failed_count,
        failed_keys=failed_keys,
        errors=errors,
    )


@router.get(
    "/info/{key}",
    response_model=CacheKeyInfo,
    summary="Get cache key information",
    description="Get metadata about a cached item",
)
async def get_cache_key_info(
    key: str = Path(..., description="Cache key to inspect"),
    current_user: User = Depends(get_current_user),
) -> CacheKeyInfo:
    """Get information about a cache key."""
    if not validate_cache_key(key):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid cache key format"
        )

    try:
        user_key = get_user_cache_key(current_user.id, key)
        exists = await cache.exists(user_key)
        ttl = await cache.ttl(user_key) if hasattr(cache, "ttl") and exists else None

        # Estimate size if key exists
        size = None
        if exists:
            try:
                data = await cache.get(user_key)
                size = len(json.dumps(data)) if data else 0
            except Exception:
                pass  # Size estimation failed, continue without it

        return CacheKeyInfo(
            key=key, exists=exists, ttl=ttl if ttl and ttl > 0 else None, size=size
        )

    except Exception as e:
        logger.error(
            f"Error getting cache key info for {key}, user {current_user.id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get cache key information",
        )


@router.get(
    "/health",
    response_model=CacheHealthResponse,
    summary="Cache health check",
    description="Check the health and performance of the cache system",
)
async def cache_health_check() -> CacheHealthResponse:
    """Perform cache health check."""
    try:
        health_info = await cache.health_check()
        return CacheHealthResponse(**health_info)
    except Exception as e:
        logger.error(f"Cache health check failed: {e}")
        return CacheHealthResponse(
            status="unhealthy", response_time_ms=0.0, connection="failed", error=str(e)
        )


@router.get(
    "/stats",
    response_model=UserCacheStatsResponse,
    summary="Get user cache statistics",
    description="Get detailed statistics about the current user's cache usage",
)
async def get_user_cache_stats(
    current_user: User = Depends(get_current_user),
) -> UserCacheStatsResponse:
    """Get cache statistics for current user."""
    try:
        user_pattern = f"user:{current_user.id}:*"
        user_keys = await cache.keys(user_pattern)

        total_keys = len(user_keys)
        total_size_bytes = 0
        key_categories = {}
        oldest_age = None
        newest_age = None

        for key in user_keys:
            try:
                # Get data size
                data = await cache.get(key)
                if data:
                    size = len(json.dumps(data))
                    total_size_bytes += size

                # Categorize keys
                key_parts = key.split(":")
                if len(key_parts) >= 3:
                    category = key_parts[2].split("_")[0]  # First part after user:id:
                    key_categories[category] = key_categories.get(category, 0) + 1

                # Get TTL for age calculation (approximate)
                ttl = await cache.ttl(key)
                if ttl:
                    # This is an approximation - actual age would need timestamp tracking
                    age = settings.CACHE_DEFAULT_TTL - ttl
                    if oldest_age is None or age > oldest_age:
                        oldest_age = age
                    if newest_age is None or age < newest_age:
                        newest_age = age

            except Exception as e:
                logger.warning(f"Error processing cache key {key}: {e}")
                continue

        return UserCacheStatsResponse(
            user_id=current_user.id,
            total_keys=total_keys,
            total_size_bytes=total_size_bytes if total_size_bytes > 0 else None,
            oldest_key_age=oldest_age,
            newest_key_age=newest_age,
            key_categories=key_categories,
        )

    except Exception as e:
        logger.error(f"Error getting cache stats for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get cache statistics",
        )


@router.post(
    "/search",
    response_model=CacheSearchResponse,
    summary="Search cache keys",
    description="Search for cache keys matching a pattern",
)
async def search_cache_keys(
    request: CacheSearchRequest, current_user: User = Depends(get_current_user)
) -> CacheSearchResponse:
    """Search cache keys for current user."""
    try:
        # Scope search to user's keys
        user_pattern = f"user:{current_user.id}:{request.pattern}"
        all_keys = await cache.keys(user_pattern)

        # Remove user prefix from results for cleaner response
        user_prefix = f"user:{current_user.id}:"
        clean_keys = [key.replace(user_prefix, "") for key in all_keys]

        total_found = len(clean_keys)
        limited = total_found > request.limit

        # Apply limit
        if limited:
            clean_keys = clean_keys[: request.limit]

        logger.debug(
            f"Cache search for user {current_user.id}, pattern: {request.pattern}, found: {total_found}"
        )

        return CacheSearchResponse(
            keys=clean_keys, total_found=total_found, limited=limited
        )

    except Exception as e:
        logger.error(f"Error searching cache keys for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search cache keys",
        )


@router.post(
    "/invalidate",
    response_model=CacheInvalidationResponse,
    summary="Invalidate cache patterns",
    description="Invalidate cache entries matching specified patterns",
)
async def invalidate_cache_patterns(
    request: CacheInvalidationRequest, current_user: User = Depends(get_current_user)
) -> CacheInvalidationResponse:
    """Invalidate cache patterns for current user."""
    if not request.confirm:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Must set confirm=true for cache invalidation",
        )

    try:
        total_invalidated = 0
        processed_patterns = []
        errors = []

        for pattern in request.patterns:
            try:
                # Scope pattern to user's keys
                user_pattern = f"user:{current_user.id}:{pattern}"
                invalidated = await cache.delete_pattern(user_pattern)
                total_invalidated += invalidated
                processed_patterns.append(pattern)

                logger.debug(
                    f"Invalidated {invalidated} keys for pattern {pattern}, user {current_user.id}"
                )

            except Exception as e:
                error_msg = f"Failed to invalidate pattern '{pattern}': {str(e)}"
                errors.append(error_msg)
                logger.error(
                    f"Cache invalidation error for user {current_user.id}, pattern {pattern}: {e}"
                )

        return CacheInvalidationResponse(
            invalidated_count=total_invalidated,
            patterns_processed=processed_patterns,
            errors=errors,
        )

    except Exception as e:
        logger.error(
            f"Error invalidating cache patterns for user {current_user.id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to invalidate cache patterns",
        )


# Admin-only endpoints
@router.get(
    "/admin/stats",
    response_model=CacheStatsResponse,
    summary="Get global cache statistics",
    description="Get global cache statistics (admin only)",
    dependencies=[Depends(require_admin_permission)],
)
async def get_global_cache_stats() -> CacheStatsResponse:
    """Get global cache statistics (admin only)."""
    try:
        # Get all keys
        all_keys = await cache.keys("*")
        user_keys = await cache.keys("user:*")

        # Get Redis info if available
        redis = await cache._get_redis()
        info = await redis.info() if hasattr(redis, "info") else {}

        memory_usage = info.get("used_memory_human", "Unknown")
        uptime = info.get("uptime_in_seconds", 0)
        uptime_str = (
            f"{uptime // 3600}h {(uptime % 3600) // 60}m" if uptime else "Unknown"
        )

        return CacheStatsResponse(
            total_keys=len(all_keys),
            user_keys=len(user_keys),
            memory_usage=memory_usage,
            hit_rate=None,  # Would need additional tracking
            uptime=uptime_str,
        )

    except Exception as e:
        logger.error(f"Error getting global cache stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get global cache statistics",
        )
