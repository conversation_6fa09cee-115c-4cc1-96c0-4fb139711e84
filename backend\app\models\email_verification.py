"""Email verification model."""

from datetime import datetime
from sqlalchemy import Column, String, DateTime, Foreign<PERSON>ey, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.models.base import BaseModel
from app.core.constants import TokenType


class EmailVerification(BaseModel):
    __tablename__ = "email_verifications"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    email = Column(String, nullable=False)
    token = Column(String, unique=True, index=True, nullable=False)
    token_type = Column(String, default=TokenType.EMAIL_VERIFICATION)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    is_used = Column(Boolean, default=False)

    # Verification metadata
    ip_address = Column(String)
    user_agent = Column(String)

    # Timestamps
    verified_at = Column(DateTime(timezone=True))

    # Relationships
    user = relationship("User", back_populates="email_verifications")

    def is_expired(self) -> bool:
        """Check if verification token is expired."""
        from datetime import datetime, timezone

        now = datetime.now(timezone.utc)
        return now > self.expires_at

    def is_valid(self) -> bool:
        """Check if verification token is valid (not used and not expired)."""
        return not self.is_used and not self.is_expired()

    def mark_as_used(self):
        """Mark verification token as used."""
        self.is_used = True
        self.verified_at = datetime.utcnow()
