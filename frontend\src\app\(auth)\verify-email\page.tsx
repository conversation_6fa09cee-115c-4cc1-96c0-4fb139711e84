"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { CheckCircle, XCircle, Loader2, Mail } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface VerificationState {
  status: "loading" | "success" | "error" | "invalid";
  message: string;
}

export default function VerifyEmailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [verificationState, setVerificationState] = useState<VerificationState>(
    {
      status: "loading",
      message: "Verifying your email...",
    }
  );

  useEffect(() => {
    const token = searchParams.get("token");

    if (!token) {
      setVerificationState({
        status: "invalid",
        message: "Invalid verification link. No token provided.",
      });
      return;
    }

    verifyEmail(token);
  }, [searchParams]);

  const verifyEmail = async (token: string) => {
    try {
      // Send token as query parameter to match backend API specification
      const url = new URL(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/auth/verify-email`
      );
      url.searchParams.append("token", token);

      const response = await fetch(url.toString(), {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();

      if (response.ok) {
        setVerificationState({
          status: "success",
          message: data.message || "Email verified successfully!",
        });

        toast.success("Email verified successfully!", {
          description: "You can now sign in to your account.",
        });

        // Redirect to login page after 3 seconds
        setTimeout(() => {
          router.push("/login?message=email-verified");
        }, 3000);
      } else {
        setVerificationState({
          status: "error",
          message:
            data.detail || "Email verification failed. Please try again.",
        });

        toast.error("Email verification failed", {
          description:
            data.detail ||
            "Please try again or request a new verification link.",
        });
      }
    } catch (error) {
      console.error("Email verification error:", error);
      setVerificationState({
        status: "error",
        message: "Network error. Please check your connection and try again.",
      });

      toast.error("Network error", {
        description: "Please check your connection and try again.",
      });
    }
  };

  const handleResendVerification = () => {
    // Redirect to a resend verification page or show a form
    router.push("/login?message=resend-verification");
  };

  const handleGoToLogin = () => {
    router.push("/login");
  };

  const getIcon = () => {
    switch (verificationState.status) {
      case "loading":
        return <Loader2 className="h-16 w-16 animate-spin text-blue-500" />;
      case "success":
        return <CheckCircle className="h-16 w-16 text-green-500" />;
      case "error":
      case "invalid":
        return <XCircle className="h-16 w-16 text-red-500" />;
      default:
        return <Mail className="h-16 w-16 text-gray-500" />;
    }
  };

  const getTitle = () => {
    switch (verificationState.status) {
      case "loading":
        return "Verifying Email...";
      case "success":
        return "Email Verified!";
      case "error":
        return "Verification Failed";
      case "invalid":
        return "Invalid Link";
      default:
        return "Email Verification";
    }
  };

  const getDescription = () => {
    switch (verificationState.status) {
      case "loading":
        return "Please wait while we verify your email address.";
      case "success":
        return "Your email has been successfully verified. You can now access all features of your account.";
      case "error":
        return "We encountered an issue while verifying your email. You can try again or request a new verification link.";
      case "invalid":
        return "The verification link is invalid or has expired. Please request a new verification email.";
      default:
        return "Verifying your email address...";
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">{getIcon()}</div>
          <CardTitle className="text-2xl font-bold">{getTitle()}</CardTitle>
          <CardDescription className="text-center">
            {getDescription()}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <p className="text-sm text-center text-muted-foreground">
            {verificationState.message}
          </p>

          <div className="space-y-2">
            {verificationState.status === "success" && (
              <Button onClick={handleGoToLogin} className="w-full">
                Continue to Sign In
              </Button>
            )}

            {(verificationState.status === "error" ||
              verificationState.status === "invalid") && (
              <>
                <Button onClick={handleResendVerification} className="w-full">
                  Request New Verification Email
                </Button>
                <Button
                  onClick={handleGoToLogin}
                  variant="outline"
                  className="w-full"
                >
                  Back to Sign In
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
