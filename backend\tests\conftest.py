"""Test configuration and fixtures."""
import asyncio
import pytest
import pytest_asyncio
from typing import As<PERSON><PERSON>enerator, Generator
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.database.base import Base
from app.database.session import get_async_db
from app.core.config import settings
from app.models.user import User
from app.crud.user import user_crud
from app.schemas.user import UserCreate

# Test database URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

# Create test engine
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    poolclass=StaticPool,
    connect_args={"check_same_thread": False},
)

# Create test session factory
TestSessionLocal = sessionmaker(
    test_engine, class_=AsyncSession, expire_on_commit=False
)


@pytest_asyncio.fixture
async def async_db() -> AsyncGenerator[AsyncSession, None]:
    """Create test database session."""
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    async with TestSessionLocal() as session:
        yield session
    
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture
def client(async_db: AsyncSession) -> Generator[TestClient, None, None]:
    """Create test client with database dependency override."""
    def get_test_db():
        return async_db
    
    app.dependency_overrides[get_async_db] = get_test_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest_asyncio.fixture
async def test_user(async_db: AsyncSession) -> User:
    """Create a test user."""
    user_data = UserCreate(
        email="<EMAIL>",
        password="testpassword123",
        full_name="Test User"
    )
    user = await user_crud.create_async(async_db, obj_in=user_data)
    return user


@pytest_asyncio.fixture
async def admin_user(async_db: AsyncSession) -> User:
    """Create an admin test user."""
    user_data = UserCreate(
        email="<EMAIL>",
        password="adminpassword123",
        full_name="Admin User"
    )
    user = await user_crud.create_async(async_db, obj_in=user_data)
    user.role = "admin"
    user.is_verified = True
    await async_db.commit()
    await async_db.refresh(user)
    return user


@pytest.fixture
def test_user_token(client: TestClient, test_user: User) -> str:
    """Get access token for test user."""
    response = client.post(
        f"{settings.API_V1_STR}/auth/login",
        data={"username": test_user.email, "password": "testpassword123"}
    )
    return response.json()["access_token"]


@pytest.fixture
def admin_user_token(client: TestClient, admin_user: User) -> str:
    """Get access token for admin user."""
    response = client.post(
        f"{settings.API_V1_STR}/auth/login",
        data={"username": admin_user.email, "password": "adminpassword123"}
    )
    return response.json()["access_token"]


@pytest.fixture
def auth_headers(test_user_token: str) -> dict:
    """Get authorization headers for test user."""
    return {"Authorization": f"Bearer {test_user_token}"}


@pytest.fixture
def admin_headers(admin_user_token: str) -> dict:
    """Get authorization headers for admin user."""
    return {"Authorization": f"Bearer {admin_user_token}"}


# Configure asyncio for pytest
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()
