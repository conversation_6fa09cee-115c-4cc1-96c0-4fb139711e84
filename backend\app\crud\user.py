"""User CRUD operations."""

from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.sql import func

from app.crud.base import CRUDBase
from app.models.user import User
from app.schemas.user import User<PERSON>reate, UserUpdate
from app.core.security import get_password_hash, verify_password


class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    def get_by_email(self, db: Session, *, email: str) -> Optional[User]:
        return db.query(User).filter(User.email == email).first()

    async def get_by_email_async(
        self, db: AsyncSession, *, email: str
    ) -> Optional[User]:
        result = await db.execute(select(User).where(User.email == email))
        return result.scalar_one_or_none()

    def create(self, db: Session, *, obj_in: UserCreate) -> User:
        db_obj = User(
            email=obj_in.email,
            hashed_password=get_password_hash(obj_in.password),
            full_name=obj_in.full_name,
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    async def create_async(self, db: AsyncSession, *, obj_in: UserCreate) -> User:
        db_obj = User(
            email=obj_in.email,
            hashed_password=get_password_hash(obj_in.password),
            full_name=obj_in.full_name,
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    def authenticate(self, db: Session, *, email: str, password: str) -> Optional[User]:
        user = self.get_by_email(db, email=email)
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user

    async def authenticate_async(
        self, db: AsyncSession, *, email: str, password: str
    ) -> Optional[User]:
        user = await self.get_by_email_async(db, email=email)
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user

    async def update_last_login(self, db: AsyncSession, *, user_id: str) -> None:
        """Update user's last login timestamp."""
        await db.execute(
            update(User).where(User.id == user_id).values(last_login=func.now())
        )
        await db.commit()

    async def verify_email(self, db: AsyncSession, *, user_id: str) -> None:
        """Mark user's email as verified."""
        await db.execute(
            update(User).where(User.id == user_id).values(is_verified=True)
        )
        await db.commit()

    async def update_password(
        self, db: AsyncSession, *, user_id: str, new_password: str
    ) -> None:
        """Update user's password."""
        hashed_password = get_password_hash(new_password)
        await db.execute(
            update(User)
            .where(User.id == user_id)
            .values(hashed_password=hashed_password, password_changed_at=func.now())
        )
        await db.commit()

    def is_active(self, user: User) -> bool:
        return user.is_active

    def is_verified(self, user: User) -> bool:
        return user.is_verified


user_crud = CRUDUser(User)
