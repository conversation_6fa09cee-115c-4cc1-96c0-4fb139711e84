# Database
DATABASE_URL=postgresql://user:password@localhost/dbname

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000","https://yourdomain.com"]

# Email
RESEND_API_KEY=your-resend-api-key-here

# Environment
ENVIRONMENT=development
DEBUG=true

# Logging Configuration
# Console log level: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL_CONSOLE=WARNING
# File log level: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL_FILE=INFO