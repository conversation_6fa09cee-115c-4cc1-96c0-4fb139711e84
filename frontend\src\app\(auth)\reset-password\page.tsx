"use client";

import { useActionState, useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { Eye, EyeOff, Lock, Loader2, CheckCircle, XCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { resetPasswordAction } from "@/lib/actions/auth-actions";

export default function ResetPasswordPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [token, setToken] = useState<string | null>(null);

  const [state, formAction, isPending] = useActionState(resetPasswordAction, {
    success: false,
    message: "",
    errors: {},
  });

  // Get token from URL params
  useEffect(() => {
    const urlToken = searchParams.get('token');
    setToken(urlToken);
  }, [searchParams]);

  // Handle success state
  useEffect(() => {
    if (state.success) {
      toast.success("Password reset successful!", {
        description: "Your password has been updated. You can now sign in with your new password.",
        duration: 5000,
      });
      
      // Redirect to login after success
      setTimeout(() => {
        router.push("/login?message=password-reset-success");
      }, 2000);
    }
  }, [state.success, router]);

  // Handle errors
  useEffect(() => {
    if (state.message && !state.success) {
      toast.error("Password reset failed", {
        description: state.message,
        duration: 5000,
      });
    }
  }, [state.message, state.success]);

  const getFieldError = (fieldName: string) => {
    if (state.errors && Array.isArray(state.errors)) {
      const fieldError = state.errors.find((error: any) => error.path?.[0] === fieldName);
      if (fieldError) {
        return fieldError.message;
      }
    }
    return undefined;
  };

  const handleBackToLogin = () => {
    router.push("/login");
  };

  // Show error if no token
  if (!token) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <XCircle className="h-12 w-12 text-red-500" />
            </div>
            <CardTitle className="text-2xl font-bold">Invalid Reset Link</CardTitle>
            <CardDescription>
              The password reset link is invalid or has expired. Please request a new one.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleBackToLogin} className="w-full">
              Back to Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {state.success ? (
              <CheckCircle className="h-12 w-12 text-green-500" />
            ) : (
              <Lock className="h-12 w-12 text-blue-500" />
            )}
          </div>
          <CardTitle className="text-2xl font-bold">
            {state.success ? "Password Updated!" : "Reset Your Password"}
          </CardTitle>
          <CardDescription className="text-center">
            {state.success
              ? "Your password has been successfully updated."
              : "Enter your new password below."}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {!state.success ? (
            <form action={formAction} className="space-y-4">
              {/* Hidden token field */}
              <input type="hidden" name="token" value={token} />

              <div className="space-y-2">
                <label htmlFor="password" className="text-sm font-medium text-gray-700">
                  New Password
                </label>
                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your new password"
                    required
                    disabled={isPending}
                    className={getFieldError("password") ? "border-red-500 pr-10" : "pr-10"}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={isPending}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                    <span className="sr-only">
                      {showPassword ? "Hide password" : "Show password"}
                    </span>
                  </Button>
                </div>
                {getFieldError("password") && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <span className="text-red-500">•</span>
                    {getFieldError("password")}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
                  Confirm New Password
                </label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirm your new password"
                    required
                    disabled={isPending}
                    className={getFieldError("confirmPassword") ? "border-red-500 pr-10" : "pr-10"}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    disabled={isPending}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                    <span className="sr-only">
                      {showConfirmPassword ? "Hide password" : "Show password"}
                    </span>
                  </Button>
                </div>
                {getFieldError("confirmPassword") && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <span className="text-red-500">•</span>
                    {getFieldError("confirmPassword")}
                  </p>
                )}
              </div>

              <Button 
                type="submit" 
                className="w-full" 
                disabled={isPending}
              >
                {isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating Password...
                  </>
                ) : (
                  <>
                    <Lock className="mr-2 h-4 w-4" />
                    Update Password
                  </>
                )}
              </Button>
            </form>
          ) : (
            <div className="space-y-4 text-center">
              <p className="text-sm text-muted-foreground">
                You will be redirected to the sign in page shortly.
              </p>
              <Button onClick={handleBackToLogin} className="w-full">
                Continue to Sign In
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
