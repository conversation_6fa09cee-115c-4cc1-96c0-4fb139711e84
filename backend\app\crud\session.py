"""Session CRUD operations."""
import uuid
from typing import Optional, List, Union
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.sql import func

from app.models.session import UserSession
from app.core.security import generate_session_token
from app.core.config import settings


class CRUDSession:
    async def create_session(
        self,
        db: AsyncSession,
        *,
        user_id: Union[str, uuid.UUID],
        refresh_token: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        device_info: Optional[str] = None
    ) -> UserSession:
        """Create a new user session."""
        session_token = generate_session_token()
        expires_at = datetime.utcnow() + timedelta(hours=settings.SESSION_EXPIRE_HOURS)

        # Convert user_id to UUID if it's a string
        if isinstance(user_id, str):
            user_id = uuid.UUID(user_id)

        db_obj = UserSession(
            user_id=user_id,
            session_token=session_token,
            refresh_token=refresh_token,
            expires_at=expires_at,
            ip_address=ip_address,
            user_agent=user_agent,
            device_info=device_info,
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_by_session_token(
        self, db: AsyncSession, *, session_token: str
    ) -> Optional[UserSession]:
        """Get session by session token."""
        result = await db.execute(
            select(UserSession).where(UserSession.session_token == session_token)
        )
        return result.scalar_one_or_none()

    async def get_by_refresh_token(
        self, db: AsyncSession, *, refresh_token: str
    ) -> Optional[UserSession]:
        """Get session by refresh token."""
        result = await db.execute(
            select(UserSession).where(UserSession.refresh_token == refresh_token)
        )
        return result.scalar_one_or_none()

    async def get_user_sessions(
        self, db: AsyncSession, *, user_id: str, active_only: bool = True
    ) -> List[UserSession]:
        """Get all sessions for a user."""
        query = select(UserSession).where(UserSession.user_id == user_id)
        if active_only:
            query = query.where(UserSession.is_active == True)
        
        result = await db.execute(query)
        return result.scalars().all()

    async def update_last_accessed(
        self, db: AsyncSession, *, session_id: str
    ) -> None:
        """Update session's last accessed timestamp."""
        await db.execute(
            update(UserSession)
            .where(UserSession.id == session_id)
            .values(last_accessed=func.now())
        )
        await db.commit()

    async def deactivate_session(
        self, db: AsyncSession, *, session_token: str
    ) -> bool:
        """Deactivate a session."""
        result = await db.execute(
            update(UserSession)
            .where(UserSession.session_token == session_token)
            .values(is_active=False)
        )
        await db.commit()
        return result.rowcount > 0

    async def deactivate_user_sessions(
        self, db: AsyncSession, *, user_id: str, exclude_session: Optional[str] = None
    ) -> int:
        """Deactivate all sessions for a user, optionally excluding one."""
        query = update(UserSession).where(
            UserSession.user_id == user_id,
            UserSession.is_active == True
        )
        
        if exclude_session:
            query = query.where(UserSession.session_token != exclude_session)
        
        result = await db.execute(query.values(is_active=False))
        await db.commit()
        return result.rowcount

    async def cleanup_expired_sessions(self, db: AsyncSession) -> int:
        """Remove expired sessions."""
        result = await db.execute(
            delete(UserSession).where(UserSession.expires_at < datetime.utcnow())
        )
        await db.commit()
        return result.rowcount


session_crud = CRUDSession()
