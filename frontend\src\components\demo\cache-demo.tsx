"use client";

import React, { useState, useEffect } from 'react';
import { hybridCache, userCache, appCache } from '@/lib/hybrid-cache-client';
import { enhancedApiClient } from '@/lib/enhanced-api-client';

interface CacheStats {
  memoryKeys: number;
  localStorageKeys: number;
  backendKeys: number;
  totalSize: number;
}

interface CacheItem {
  key: string;
  data: any;
  source: 'memory' | 'localStorage' | 'backend' | 'fresh';
  timestamp: number;
}

export default function CacheDemo() {
  const [stats, setStats] = useState<CacheStats>({
    memoryKeys: 0,
    localStorageKeys: 0,
    backendKeys: 0,
    totalSize: 0
  });
  
  const [recentItems, setRecentItems] = useState<CacheItem[]>([]);
  const [testKey, setTestKey] = useState('demo_data');
  const [testValue, setTestValue] = useState('{"message": "Hello Cache!", "timestamp": "2024-01-01"}');
  const [testTTL, setTestTTL] = useState(300);
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  const updateStats = async () => {
    try {
      // Count memory cache items (simplified)
      const memoryKeys = 0; // Would need access to internal cache
      
      // Count localStorage cache items
      const localStorageKeys = Object.keys(localStorage)
        .filter(key => key.startsWith('cache_')).length;
      
      // Get backend cache stats (if user is logged in)
      let backendKeys = 0;
      try {
        const response = await fetch('http://localhost:8000/api/v1/cache/stats', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          },
        });
        if (response.ok) {
          const data = await response.json();
          backendKeys = data.total_keys || 0;
        }
      } catch (error) {
        // User might not be logged in
      }

      setStats({
        memoryKeys,
        localStorageKeys,
        backendKeys,
        totalSize: 0 // Would need to calculate
      });
    } catch (error) {
      addLog(`Error updating stats: ${error}`);
    }
  };

  const testCacheSet = async () => {
    setLoading(true);
    try {
      const data = JSON.parse(testValue);
      await hybridCache.set(testKey, data, testTTL);
      
      const newItem: CacheItem = {
        key: testKey,
        data,
        source: 'fresh',
        timestamp: Date.now()
      };
      
      setRecentItems(prev => [newItem, ...prev.slice(0, 4)]);
      addLog(`✅ Set cache item: ${testKey}`);
      await updateStats();
    } catch (error) {
      addLog(`❌ Failed to set cache item: ${error}`);
    }
    setLoading(false);
  };

  const testCacheGet = async () => {
    setLoading(true);
    try {
      const startTime = Date.now();
      const data = await hybridCache.get(testKey);
      const duration = Date.now() - startTime;
      
      if (data) {
        const newItem: CacheItem = {
          key: testKey,
          data,
          source: 'memory', // Simplified - would need actual source detection
          timestamp: Date.now()
        };
        
        setRecentItems(prev => [newItem, ...prev.slice(0, 4)]);
        addLog(`✅ Retrieved cache item: ${testKey} (${duration}ms)`);
      } else {
        addLog(`❌ Cache miss for key: ${testKey}`);
      }
    } catch (error) {
      addLog(`❌ Failed to get cache item: ${error}`);
    }
    setLoading(false);
  };

  const testCacheRemove = async () => {
    setLoading(true);
    try {
      await hybridCache.remove(testKey);
      addLog(`✅ Removed cache item: ${testKey}`);
      await updateStats();
    } catch (error) {
      addLog(`❌ Failed to remove cache item: ${error}`);
    }
    setLoading(false);
  };

  const testCacheClear = async () => {
    setLoading(true);
    try {
      await hybridCache.clear();
      setRecentItems([]);
      addLog(`✅ Cleared all cache items`);
      await updateStats();
    } catch (error) {
      addLog(`❌ Failed to clear cache: ${error}`);
    }
    setLoading(false);
  };

  const testUserCache = async () => {
    setLoading(true);
    try {
      // Test user-specific cache
      const userData = {
        preferences: { theme: 'dark', language: 'en' },
        settings: { notifications: true }
      };
      
      await userCache.set('user_preferences', userData, 600);
      const retrieved = await userCache.get('user_preferences');
      
      if (retrieved) {
        addLog(`✅ User cache test successful`);
        const newItem: CacheItem = {
          key: 'user_preferences',
          data: retrieved,
          source: 'backend',
          timestamp: Date.now()
        };
        setRecentItems(prev => [newItem, ...prev.slice(0, 4)]);
      }
    } catch (error) {
      addLog(`❌ User cache test failed: ${error}`);
    }
    setLoading(false);
  };

  const testAppCache = async () => {
    setLoading(true);
    try {
      // Test app-specific cache (local only)
      const appData = {
        version: '1.0.0',
        lastUpdate: new Date().toISOString(),
        features: ['cache', 'auth', 'api']
      };
      
      await appCache.set('app_metadata', appData, 3600);
      const retrieved = await appCache.get('app_metadata');
      
      if (retrieved) {
        addLog(`✅ App cache test successful`);
        const newItem: CacheItem = {
          key: 'app_metadata',
          data: retrieved,
          source: 'localStorage',
          timestamp: Date.now()
        };
        setRecentItems(prev => [newItem, ...prev.slice(0, 4)]);
      }
    } catch (error) {
      addLog(`❌ App cache test failed: ${error}`);
    }
    setLoading(false);
  };

  useEffect(() => {
    updateStats();
    addLog('Cache demo initialized');
  }, []);

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          🚀 Hybrid Cache Demo
        </h1>
        <p className="text-gray-600 mb-6">
          Test the multi-layer caching system: Memory → localStorage → Backend Redis → Fresh Data
        </p>

        {/* Cache Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-900">Memory Cache</h3>
            <p className="text-2xl font-bold text-blue-600">{stats.memoryKeys}</p>
            <p className="text-sm text-blue-700">keys</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-semibold text-green-900">localStorage</h3>
            <p className="text-2xl font-bold text-green-600">{stats.localStorageKeys}</p>
            <p className="text-sm text-green-700">keys</p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="font-semibold text-purple-900">Backend Redis</h3>
            <p className="text-2xl font-bold text-purple-600">{stats.backendKeys}</p>
            <p className="text-sm text-purple-700">keys</p>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <h3 className="font-semibold text-orange-900">Total Size</h3>
            <p className="text-2xl font-bold text-orange-600">{stats.totalSize}</p>
            <p className="text-sm text-orange-700">bytes</p>
          </div>
        </div>

        {/* Test Controls */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Cache Operations */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Cache Operations</h3>
            
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Cache Key
                </label>
                <input
                  type="text"
                  value={testKey}
                  onChange={(e) => setTestKey(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter cache key"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Cache Value (JSON)
                </label>
                <textarea
                  value={testValue}
                  onChange={(e) => setTestValue(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="Enter JSON data"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  TTL (seconds)
                </label>
                <input
                  type="number"
                  value={testTTL}
                  onChange={(e) => setTestTTL(parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="1"
                  max="86400"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={testCacheSet}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                Set Cache
              </button>
              <button
                onClick={testCacheGet}
                disabled={loading}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
              >
                Get Cache
              </button>
              <button
                onClick={testCacheRemove}
                disabled={loading}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
              >
                Remove
              </button>
              <button
                onClick={testCacheClear}
                disabled={loading}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50"
              >
                Clear All
              </button>
            </div>

            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={testUserCache}
                disabled={loading}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50"
              >
                Test User Cache
              </button>
              <button
                onClick={testAppCache}
                disabled={loading}
                className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50"
              >
                Test App Cache
              </button>
            </div>
          </div>

          {/* Recent Cache Items */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Recent Cache Items</h3>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {recentItems.map((item, index) => (
                <div key={index} className="bg-gray-50 p-3 rounded-md">
                  <div className="flex justify-between items-start mb-2">
                    <span className="font-medium text-gray-900">{item.key}</span>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      item.source === 'memory' ? 'bg-blue-100 text-blue-800' :
                      item.source === 'localStorage' ? 'bg-green-100 text-green-800' :
                      item.source === 'backend' ? 'bg-purple-100 text-purple-800' :
                      'bg-orange-100 text-orange-800'
                    }`}>
                      {item.source}
                    </span>
                  </div>
                  <pre className="text-xs text-gray-600 bg-white p-2 rounded border overflow-x-auto">
                    {JSON.stringify(item.data, null, 2)}
                  </pre>
                </div>
              ))}
              {recentItems.length === 0 && (
                <p className="text-gray-500 text-center py-4">No recent cache items</p>
              )}
            </div>
          </div>
        </div>

        {/* Activity Log */}
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-3">Activity Log</h3>
          <div className="bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm max-h-48 overflow-y-auto">
            {logs.map((log, index) => (
              <div key={index} className="mb-1">{log}</div>
            ))}
            {logs.length === 0 && (
              <div className="text-gray-500">No activity yet...</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
