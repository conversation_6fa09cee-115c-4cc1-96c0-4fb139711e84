---
type: "manual"
---

# FastAPI Backend Development Rules & Best Practices

This guide outlines **proven, battle-tested rules** for building a **fast, secure, maintainable, and Next.js-compatible FastAPI backend**. These patterns have been successfully implemented and tested in production-grade applications with comprehensive authentication, Redis caching, monitoring, and observability features.

---

## 1. Code Style & General Best Practices

- **DRY (Don't Repeat Yourself):**
  Always extract common logic into utility functions, base classes, or dependencies. Use generic CRUD base classes and shared response schemas to eliminate duplicate code across routers, models, and services.

- **Conciseness & Clarity:**
  Prioritize readable, concise code. Use descriptive variable/function/class names. Add comprehensive docstrings to all functions/classes with parameter descriptions and return types.

- **Type Hints Everywhere:**
  Use Python’s type hints throughout (`def get_user(id: UUID) -> UserResponse:`) for editor support, code clarity, and runtime validation. Include generic types for collections and optional types.

- **Async-first Architecture:**
  Use `async def` for all endpoints, database operations, and external service calls. Prefer async drivers (SQLAlchemy 2.0+ with asyncpg, aioredis, httpx) to maximize concurrency and performance.

- **Dependency Injection Pattern:**
  Leverage FastAPI’s `Depends` extensively for shared logic (auth, DB sessions, config, caching). Create reusable dependency functions for common patterns like pagination, filtering, and permission checking.

- **Config Management:**  
  Store secrets/config in environment variables, loaded with Pydantic’s `BaseSettings`. Never hardcode secrets.

- **Error Handling:**
  Use FastAPI’s exception handlers to return consistent, informative errors. Log all errors with context.

- **Structured Logging:**
  Implement clean console output with configurable verbosity levels. Use detailed file logging with JSON formatting for production. Separate console and file log levels for optimal development experience.

- **Package Management:**
  Always use `uv` or appropriate package managers for dependency management. Never manually edit `pyproject.toml` or requirements files - use package manager commands to ensure proper version resolution and lock file updates.

---

## 2. Authentication & Authorization

Implement **full-stack auth** supporting:

- **JWT Auth:**

  - Use short-lived access tokens and refresh tokens.
  - Store JWT secret in environment variables.
  - Validate tokens on every protected endpoint.
  - Rotate secrets and invalidate tokens on password change.

- **Session Database:**

  - Implement a session table (user_id, session_token, expiry).
  - Store sessions for browser-based auth (Next.js SSR/CSR).
  - Support session invalidation (logout, browser close).

- **OAuth Integration:**

  - Use `authlib` or `fastapi-users` for Google, GitHub, etc.
  - Store minimal profile info; never expose tokens.

- **Email Verification (Resend):**

  - On signup, send verification email via Resend (or similar).
  - Store verification tokens in DB.
  - Require verified email for login.
  - Handle Resend API errors gracefully.

- **Role-Based Access Control:**
  - Decorate endpoints with roles/permissions.
  - Store roles in DB.
  - Validate permissions in dependency functions.

**Recommended Libraries:**

- [fastapi-users](https://github.com/fastapi-users/fastapi-users)
- [authlib](https://docs.authlib.org/)
- [python-jose](https://python-jose.readthedocs.io/)

---

## 3. Security

- **Password Hashing:**  
  Use `bcrypt` or `argon2` via `passlib`. Never store raw passwords.

- **CSRF Protection:**  
  For session-based flows, implement CSRF tokens.

- **CORS Configuration:**  
  Allow only trusted frontend origins (`http://localhost:3000`, production domain).

- **Rate Limiting:**  
  Use `slowapi` for rate limiting sensitive endpoints.

- **Input Validation:**  
  Use Pydantic models for all input data. Always validate data at the API boundary.

- **Security Headers:**  
  Set headers for HSTS, X-Frame-Options, etc. Use [Secure](https://pypi.org/project/secure/) package.

---

## 4. Database & ORM

- **ORM:**  
  Use SQLAlchemy 2.0+ for ORM. Prefer async drivers (e.g., `asyncpg`).
- **Migrations:**  
  Use Alembic for database migrations. Automate with `alembic revision --autogenerate`.
- **Session Management:**  
  Use FastAPI’s `Depends` for DB sessions. Always close sessions after use.
- **Data Validation:**
  Use Pydantic models for all data transfer objects (DTOs).

## 5. Caching & Performance

- **Redis Caching System:**
  Use Redis for caching with aioredis for async operations. Implement intelligent serialization using JSON for simple types and pickle for complex objects. Use connection pooling and health checks.

- **Cache Decorators:**
  Create reusable cache decorators for function results with configurable TTL. Support cache invalidation patterns and statistics collection.

- **Performance Monitoring:**
  Implement middleware for request/response timing, query performance monitoring, and slow query detection. Provide optimization recommendations.

- **Connection Management:**
  Use proper connection pooling for both database and Redis. Avoid QueuePool with async SQLAlchemy engines.

## 6. Testing

- **Test Types:**

  - Unit tests for pure functions.
  - Integration tests for DB/API.
  - Auth flow tests (sign up, login, token refresh, email verification).
  - Module tests for routers/services.

- **Tools:**

  - [pytest](https://docs.pytest.org/)
  - [httpx](https://www.python-httpx.org/) for async API calls.
  - [pytest-asyncio](https://pytest-asyncio.readthedocs.io/)
  - [pytest-cov](https://pytest-cov.readthedocs.io/) for coverage.

- **CI Integration:**  
  Add test step to Docker Compose and CI workflow.  
  Fail build on <90% coverage.

**Example Test Structure:**

```
/tests
  /unit
  /integration
  /auth
```

---

## 7. Next.js Frontend Compatibility

- **OpenAPI/Swagger:**  
  Keep OpenAPI schema up-to-date for Next.js API clients.

- **CORS:**  
  Configure CORS for Next.js dev/prod domains.

- **Cookie Handling:**  
  Set `SameSite=None; Secure` for cookies if using HTTPS.

- **Auth APIs:**  
  Support both JWT (SPA) and session auth (SSR/CSR).

- **Error Responses:**  
  Use standard HTTP status codes and JSON error bodies.  
  Next.js expects `application/json` for API errors.

- **WebSocket Support:**  
  If using realtime features, implement FastAPI `WebSocket` endpoints.

- **Versioned APIs:**  
  Prefix API routes with `/api/v1` (or `/api/v2`) for frontend stability.

---

## 8. Project Structure

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py
│   │   ├── security.py
│   │   ├── cache.py
│   │   ├── logging.py
│   │   ├── middleware.py
│   │   ├── exceptions.py
│   │   └── constants.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── deps.py
│   │   ├── main.py
│   │   └── v1/
│   │       ├── __init__.py
│   │       ├── api.py
│   │       ├── routes/
│   │       │   ├── __init__.py
│   │       │   ├── auth.py
│   │       │   ├── users.py
│   │       │   ├── items.py
│   │       │   └── monitoring.py
│   │       └── schemas/
│   │           ├── __init__.py
│   │           ├── token.py
│   │           ├── user.py
│   │           └── item.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── base.py
│   │   ├── user.py
│   │   └── item.py
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── base.py
│   │   ├── user.py
│   │   └── item.py
│   ├── database/
│   │   ├── __init__.py
│   │   ├── session.py
│   │   └── base.py
│   ├── crud/
│   │   ├── __init__.py
│   │   ├── base.py
│   │   ├── user.py
│   │   └── item.py
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── email.py
│   │   └── security.py
│   └── tests/
│       ├── __init__.py
│       ├── conftest.py
│       ├── test_auth.py
│       └── test_users.py
├── alembic/
│   ├── env.py
│   ├── README
│   ├── script.py.mako
│   └── versions/
├── docs/                  # Documentation
├── logs/                  # Log files (created at runtime)
├── scripts/               # Utility scripts
│   ├── start.sh
│   ├── test.sh
│   └── test_logging.py
├── .env
├── .env.example
├── .gitignore
├── pyproject.toml
├── README.md
└── alembic.ini
```

---

## 9. Documentation

- Document all endpoints and their auth requirements.
- Use OpenAPI tags for grouping endpoints.
- Add comprehensive README with architecture diagrams and setup instructions.
- Include Mermaid diagrams for authentication flows and system architecture.
- Document logging configuration and monitoring endpoints.

---

## 10. Example References

- [FastAPI Full-Stack Template](https://github.com/tiangolo/full-stack-fastapi-postgresql)
- [Next.js Auth Example](https://github.com/nextauthjs/next-auth-example)
- [Resend Email Verification in FastAPI](https://resend.com/docs/send-email/python)

---

## 11. Senior Tips

- **Automate Everything:**
  Use scripts/Makefiles for setup, tests, migrations, and deployment.

- **Lint & Format:**
  Enforce `black`, `flake8`, and `isort` in CI.

- **Monitor & Log:**
  Integrate comprehensive logging and monitoring (Prometheus, Sentry). Use clean console output for development and detailed file logging for production.

- **Scalability:**
  Design for statelessness; use Redis for caching and sessions, PostgreSQL for persistent data.

- **Environment Management:**
  Use environment-dependent configuration with proper defaults. Support multiple deployment environments (dev/staging/prod).

- **Performance Optimization:**
  Implement Redis caching, connection pooling, query optimization, and performance monitoring middleware.

---

> **Follow these proven, battle-tested rules to deliver a robust, production-ready FastAPI backend with comprehensive authentication, Redis caching, monitoring, and seamless Next.js compatibility.**
