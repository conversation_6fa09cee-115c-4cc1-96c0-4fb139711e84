"""FastAPI dependencies for authentication, authorization, and database sessions."""
from typing import Generator, Optional
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy import select

from app.database.session import SessionLocal, AsyncSessionLocal
from app.core.security import verify_token
from app.core.config import settings
from app.core.constants import UserRole, Permission
from app.models.user import User
from app.models.session import UserSession

# Security scheme
security = HTTPBearer()


def get_db() -> Generator[Session, None, None]:
    """Get database session dependency (sync)."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db() -> Generator[AsyncSession, None, None]:
    """Get async database session dependency."""
    async with AsyncSessionLocal() as session:
        yield session


async def get_current_user_from_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_async_db)
) -> User:
    """Get current user from JWT token."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    token = credentials.credentials
    payload = verify_token(token, "access")
    
    if payload is None:
        raise credentials_exception
    
    user_id: str = payload.get("sub")
    if user_id is None:
        raise credentials_exception
    
    # Get user from database
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    
    if user is None:
        raise credentials_exception
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    return user


async def get_current_user_from_session(
    request: Request,
    db: AsyncSession = Depends(get_async_db)
) -> Optional[User]:
    """Get current user from session token (for browser-based auth)."""
    session_token = request.cookies.get("session_token")
    
    if not session_token:
        return None
    
    # Get session from database
    result = await db.execute(
        select(UserSession).where(
            UserSession.session_token == session_token,
            UserSession.is_active == True
        )
    )
    session = result.scalar_one_or_none()
    
    if not session or not session.is_valid():
        return None
    
    # Get user
    result = await db.execute(select(User).where(User.id == session.user_id))
    user = result.scalar_one_or_none()
    
    if not user or not user.is_active:
        return None
    
    return user


# Primary authentication dependency (tries JWT first, then session)
async def get_current_user(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> User:
    """Get current authenticated user (JWT or session)."""
    user = None
    
    # Try JWT authentication first
    if credentials:
        try:
            user = await get_current_user_from_token(credentials, db)
        except HTTPException:
            pass
    
    # Fall back to session authentication
    if not user:
        user = await get_current_user_from_session(request, db)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated"
        )
    
    return user


def require_role(required_role: str):
    """Dependency factory for role-based access control."""
    async def role_checker(current_user: User = Depends(get_current_user)) -> User:
        if current_user.role != required_role and not current_user.is_admin():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        return current_user
    return role_checker


def require_permission(permission: str):
    """Dependency factory for permission-based access control."""
    async def permission_checker(current_user: User = Depends(get_current_user)) -> User:
        if not current_user.has_permission(permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        return current_user
    return permission_checker


# Common role dependencies
get_admin_user = require_role(UserRole.ADMIN)
get_moderator_user = require_role(UserRole.MODERATOR)

# Common permission dependencies
require_admin_permission = require_permission(Permission.ADMIN)
require_write_permission = require_permission(Permission.WRITE)
require_delete_permission = require_permission(Permission.DELETE)
