2025-08-01 22:37:21 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:56 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:38:57 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:39:09 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 'Secure' object has no attribute 'framework'
2025-08-01 22:45:11 - app.app.api.v1.routes.monitoring - ERROR - monitoring - app_info:257 - App info endpoint failed: module 'psutil' has no attribute 'platform'
2025-08-01 22:45:32 - app.app.api.v1.routes.monitoring - ERROR - monitoring - app_info:257 - App info endpoint failed: module 'psutil' has no attribute 'platform'
2025-08-01 22:51:02 - app.utils.email - ERROR - email - _send_email:48 - Failed to send <NAME_EMAIL>: Unknown error
2025-08-01 22:51:02 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/register - 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('90938a26-219e-4f47-b59c-ae77f2eeefce')}
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 328, in app
    content = await serialize_response(
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 177, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('90938a26-219e-4f47-b59c-ae77f2eeefce')}

2025-08-01 22:51:02 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('90938a26-219e-4f47-b59c-ae77f2eeefce')}

2025-08-01 22:51:05 - app.utils.email - ERROR - email - _send_email:48 - Failed to send <NAME_EMAIL>: Unknown error
2025-08-01 22:51:05 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/register - 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('95ef8a09-7ba8-4f1d-86eb-839c2ead59c6')}
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 328, in app
    content = await serialize_response(
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 177, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('95ef8a09-7ba8-4f1d-86eb-839c2ead59c6')}

2025-08-01 22:51:05 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('95ef8a09-7ba8-4f1d-86eb-839c2ead59c6')}

2025-08-01 22:51:09 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:09 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:12 - app.utils.email - ERROR - email - _send_email:48 - Failed to send <NAME_EMAIL>: Unknown error
2025-08-01 22:51:15 - app.utils.email - ERROR - email - _send_email:48 - Failed to send <NAME_EMAIL>: Unknown error
2025-08-01 22:51:15 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/register - 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('289f7e8f-27fc-47e0-a2ef-249e56b60433')}
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 328, in app
    content = await serialize_response(
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 177, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('289f7e8f-27fc-47e0-a2ef-249e56b60433')}

2025-08-01 22:51:15 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('289f7e8f-27fc-47e0-a2ef-249e56b60433')}

2025-08-01 22:51:19 - app.utils.email - ERROR - email - _send_email:48 - Failed to send <NAME_EMAIL>: Unknown error
2025-08-01 22:51:19 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/register - 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('ce497441-22d2-4c79-af2d-4b379cec2e01')}
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 328, in app
    content = await serialize_response(
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 177, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('ce497441-22d2-4c79-af2d-4b379cec2e01')}

2025-08-01 22:51:19 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('ce497441-22d2-4c79-af2d-4b379cec2e01')}

2025-08-01 22:51:19 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:19 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:20 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:20 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:20 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:20 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:20 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:20 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:21 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:21 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:34 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:34 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:34 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:34 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:35 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:35 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:35 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:35 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:35 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:35 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:36 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:36 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:36 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:36 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:37 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:37 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:37 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:37 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:38 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:38 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:38 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:38 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:39 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:39 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:39 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:39 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:40 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:40 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:40 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/login - object JSONResponse can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 59, in wrapped_app
    response = await handler(conn, exc)
  File "D:\nextjs\webapp\backend\app\core\middleware.py", line 61, in rate_limit_handler
    return await _rate_limit_exceeded_handler(request, exc)
TypeError: object JSONResponse can't be used in 'await' expression
2025-08-01 22:51:40 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: object JSONResponse can't be used in 'await' expression
2025-08-01 23:49:40 - app.app.core.cache - ERROR - cache - cleanup_cache_task:341 - Cache cleanup task error: 'RedisCache' object has no attribute 'cleanup_expired'
2025-08-01 23:50:13 - app.app.core.cache - ERROR - cache - cleanup_cache_task:336 - Cache cleanup task error: 'RedisCache' object has no attribute 'cleanup_expired'
2025-08-01 23:50:51 - uvicorn.error - ERROR - on - send:121 - Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\contextlib.py", line 199, in __aenter__
    return await anext(self.gen)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 134, in merged_lifespan
    async with original_context(app) as maybe_original_state:
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\contextlib.py", line 199, in __aenter__
    return await anext(self.gen)
  File "D:\nextjs\webapp\backend\app\main.py", line 37, in lifespan
    asyncio.create_task(cleanup_cache_task())
NameError: name 'cleanup_cache_task' is not defined

2025-08-01 23:50:51 - uvicorn.error - ERROR - on - startup:59 - Application startup failed. Exiting.
2025-08-01 23:53:50 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Event loop is closed
2025-08-01 23:53:50 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: Task <Task pending name='starlette.middleware.base.BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro' coro=<BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro() running at D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py:144> cb=[TaskGroup._spawn.<locals>.task_done() at D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\_backends\_asyncio.py:794]> got Future <Future pending> attached to a different loop
2025-08-01 23:53:50 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:53:50 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:53:50 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:54:32 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Event loop is closed
2025-08-01 23:54:32 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: Task <Task pending name='starlette.middleware.base.BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro' coro=<BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro() running at D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py:144> cb=[TaskGroup._spawn.<locals>.task_done() at D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\_backends\_asyncio.py:794]> got Future <Future pending> attached to a different loop
2025-08-01 23:54:32 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:54:32 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:54:32 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:55:30 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Event loop is closed
2025-08-01 23:55:30 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: Task <Task pending name='starlette.middleware.base.BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro' coro=<BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro() running at D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py:144> cb=[TaskGroup._spawn.<locals>.task_done() at D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\_backends\_asyncio.py:794]> got Future <Future pending> attached to a different loop
2025-08-01 23:55:30 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:55:30 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:55:30 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:55:42 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Event loop is closed
2025-08-01 23:55:42 - app.app.api.v1.routes.monitoring - ERROR - monitoring - get_metrics:217 - Metrics endpoint failed: 'total_entries'
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - clear_all:140 - Cache clear all error: Task <Task pending name='starlette.middleware.base.BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro' coro=<BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro() running at D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py:144> cb=[TaskGroup._spawn.<locals>.task_done() at D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\_backends\_asyncio.py:794]> got Future <Future pending> attached to a different loop
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - clear_all:140 - Cache clear all error: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:55:43 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:55:44 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:55:44 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:55:44 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:55:44 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:55:56 - app.app.api.v1.routes.monitoring - ERROR - monitoring - get_metrics:217 - Metrics endpoint failed: 'total_entries'
2025-08-01 23:59:55 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Event loop is closed
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - clear_all:140 - Cache clear all error: Task <Task pending name='starlette.middleware.base.BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro' coro=<BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro() running at D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py:144> cb=[TaskGroup._spawn.<locals>.task_done() at D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\_backends\_asyncio.py:794]> got Future <Future pending> attached to a different loop
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - clear_all:140 - Cache clear all error: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:59:56 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-01 23:59:57 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-01 23:59:57 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-01 23:59:57 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-01 23:59:57 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Event loop is closed
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - clear_all:140 - Cache clear all error: Task <Task pending name='starlette.middleware.base.BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro' coro=<BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro() running at D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py:144> cb=[TaskGroup._spawn.<locals>.task_done() at D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\_backends\_asyncio.py:794]> got Future <Future pending> attached to a different loop
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - clear_all:140 - Cache clear all error: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-02 00:02:14 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-02 00:02:16 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key health:check: await wasn't used with future
2025-08-02 00:02:16 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key health:check: await wasn't used with future
2025-08-02 00:02:16 - app.app.core.cache - ERROR - cache - delete:105 - Cache delete error for key health:check: await wasn't used with future
2025-08-02 00:02:16 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: await wasn't used with future
2025-08-02 00:02:36 - app.app.core.cache - ERROR - cache - clear_all:140 - Cache clear all error: Event loop is closed
2025-08-02 00:02:36 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Task <Task pending name='starlette.middleware.base.BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro' coro=<BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.coro() running at D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py:144> cb=[TaskGroup._spawn.<locals>.task_done() at D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\_backends\_asyncio.py:794]> got Future <Future pending> attached to a different loop
2025-08-02 00:02:36 - app.app.core.cache - ERROR - cache - set:95 - Cache set error for key test:integration: await wasn't used with future
2025-08-02 00:02:36 - app.app.core.cache - ERROR - cache - get:69 - Cache get error for key test:integration: await wasn't used with future
2025-08-02 00:04:08 - app.app.core.cache - ERROR - cache - get_stats:160 - Cache stats error: Event loop is closed
2025-08-02 00:18:45 - uvicorn.error - ERROR - on - send:134 - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 603, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\base_events.py", line 1909, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\uvicorn\server.py", line 70, in serve
    with self.capture_signals():
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\contextlib.py", line 142, in __exit__
    next(self.gen)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\uvicorn\server.py", line 331, in capture_signals
    signal.raise_signal(captured_signal)
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 701, in lifespan
    await receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\uvicorn\lifespan\on.py", line 137, in receive
    return await self.receive_queue.get()
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.10.18-windows-x86_64-none\lib\asyncio\queues.py", line 159, in get
    await getter
asyncio.exceptions.CancelledError

2025-08-02 00:31:36 - app.test - ERROR - <string> - <module>:17 - This is an ERROR message
2025-08-02 00:34:52 - app.test - ERROR - test_logging - test_logging_levels:43 - ❌ ERROR: This is an error message
2025-08-02 00:35:05 - app.test - ERROR - test_logging - test_logging_levels:43 - ❌ ERROR: This is an error message
2025-08-02 17:27:17 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/register - 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('6019f477-eab9-4ad6-8dd8-3fa684ed3659')}
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 328, in app
    content = await serialize_response(
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 177, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('6019f477-eab9-4ad6-8dd8-3fa684ed3659')}

2025-08-02 17:27:17 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'id'), 'msg': 'Input should be a valid string', 'input': UUID('6019f477-eab9-4ad6-8dd8-3fa684ed3659')}

2025-08-02 18:17:47 - app.performance - ERROR - performance - dispatch:62 - Request error: POST /api/v1/auth/verify-email - can't compare offset-naive and offset-aware datetimes
Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in call_next
    message = await recv_stream.receive()
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\anyio\streams\memory.py", line 126, in receive
    raise EndOfStream from None
anyio.EndOfStream

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\nextjs\webapp\backend\app\core\performance.py", line 29, in dispatch
    response = await call_next(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 159, in call_next
    raise app_exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\starlette\routing.py", line 75, in app
    response = await f(request)
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 302, in app
    raw_response = await run_endpoint_function(
  File "D:\nextjs\webapp\backend\.venv\lib\site-packages\fastapi\routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
  File "D:\nextjs\webapp\backend\app\api\v1\routes\auth.py", line 228, in verify_email
    if not verification or not verification.is_valid():
  File "D:\nextjs\webapp\backend\app\models\email_verification.py", line 38, in is_valid
    return not self.is_used and not self.is_expired()
  File "D:\nextjs\webapp\backend\app\models\email_verification.py", line 34, in is_expired
    return datetime.utcnow() > self.expires_at
TypeError: can't compare offset-naive and offset-aware datetimes
2025-08-02 18:17:48 - app.app.core.versioning - ERROR - versioning - dispatch:193 - API versioning middleware error: can't compare offset-naive and offset-aware datetimes
