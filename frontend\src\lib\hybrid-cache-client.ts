// Hybrid caching strategy: localStorage + backend Redis via API
import { typedApiClient } from "./typed-api-client";

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

interface CacheConfig {
  defaultTTL: number;
  useBackendCache: boolean;
  fallbackToLocalStorage: boolean;
}

export class HybridCacheClient {
  private config: CacheConfig;
  private localCache = new Map<string, CacheItem<any>>();

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      defaultTTL: 5 * 60 * 1000, // 5 minutes default
      useBackendCache: true,
      fallbackToLocalStorage: true,
      ...config,
    };
  }

  // Get data with hybrid caching strategy
  async get<T>(key: string, fetchFn?: () => Promise<T>): Promise<T | null> {
    try {
      // 1. Check memory cache first (fastest)
      const memoryItem = this.localCache.get(key);
      if (memoryItem && !this.isExpired(memoryItem)) {
        console.log(`Cache HIT (memory): ${key}`);
        return memoryItem.data;
      }

      // 2. Check localStorage (fast, persists across sessions)
      if (this.config.fallbackToLocalStorage) {
        const localItem = this.getFromLocalStorage<T>(key);
        if (localItem && !this.isExpired(localItem)) {
          console.log(`Cache HIT (localStorage): ${key}`);
          // Update memory cache
          this.localCache.set(key, localItem);
          return localItem.data;
        }
      }

      // 3. Check backend Redis cache via API (slower, but shared across devices)
      if (this.config.useBackendCache) {
        try {
          const backendData = await this.getFromBackendCache<T>(key);
          if (backendData) {
            console.log(`Cache HIT (backend Redis): ${key}`);
            // Update local caches
            this.setInAllCaches(key, backendData, this.config.defaultTTL);
            return backendData;
          }
        } catch (error) {
          console.warn(`Backend cache failed for ${key}:`, error);
        }
      }

      // 4. Fetch fresh data if provided
      if (fetchFn) {
        console.log(`Cache MISS: Fetching fresh data for ${key}`);
        const freshData = await fetchFn();
        // Store in all available caches
        await this.setInAllCaches(key, freshData, this.config.defaultTTL);
        return freshData;
      }

      return null;
    } catch (error) {
      console.error(`Cache get error for ${key}:`, error);
      return null;
    }
  }

  // Set data in all available caches
  async set<T>(key: string, data: T, ttl: number = this.config.defaultTTL): Promise<void> {
    await this.setInAllCaches(key, data, ttl);
  }

  // Remove from all caches
  async remove(key: string): Promise<void> {
    // Remove from memory
    this.localCache.delete(key);

    // Remove from localStorage
    if (this.config.fallbackToLocalStorage) {
      localStorage.removeItem(`cache_${key}`);
    }

    // Remove from backend cache
    if (this.config.useBackendCache) {
      try {
        await this.removeFromBackendCache(key);
      } catch (error) {
        console.warn(`Failed to remove ${key} from backend cache:`, error);
      }
    }
  }

  // Clear all caches
  async clear(): Promise<void> {
    // Clear memory cache
    this.localCache.clear();

    // Clear localStorage cache items
    if (this.config.fallbackToLocalStorage) {
      const keys = Object.keys(localStorage).filter(key => key.startsWith('cache_'));
      keys.forEach(key => localStorage.removeItem(key));
    }

    // Clear backend cache (user-specific)
    if (this.config.useBackendCache) {
      try {
        await this.clearBackendCache();
      } catch (error) {
        console.warn('Failed to clear backend cache:', error);
      }
    }
  }

  // Private helper methods
  private async setInAllCaches<T>(key: string, data: T, ttl: number): Promise<void> {
    const cacheItem: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
    };

    // Set in memory cache
    this.localCache.set(key, cacheItem);

    // Set in localStorage
    if (this.config.fallbackToLocalStorage) {
      try {
        localStorage.setItem(`cache_${key}`, JSON.stringify(cacheItem));
      } catch (error) {
        console.warn(`Failed to set ${key} in localStorage:`, error);
      }
    }

    // Set in backend cache
    if (this.config.useBackendCache) {
      try {
        await this.setInBackendCache(key, data, ttl);
      } catch (error) {
        console.warn(`Failed to set ${key} in backend cache:`, error);
      }
    }
  }

  private getFromLocalStorage<T>(key: string): CacheItem<T> | null {
    try {
      const item = localStorage.getItem(`cache_${key}`);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.warn(`Failed to get ${key} from localStorage:`, error);
      return null;
    }
  }

  private async getFromBackendCache<T>(key: string): Promise<T | null> {
    try {
      // Call the backend cache API endpoint
      const response = await fetch(`http://localhost:8000/api/v1/cache/${encodeURIComponent(key)}`, {
        headers: {
          'Authorization': `Bearer ${this.getAccessToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        return result.data;
      } else if (response.status === 404) {
        // Cache miss is normal, not an error
        return null;
      }
      return null;
    } catch (error) {
      console.warn(`Backend cache get failed for ${key}:`, error);
      return null;
    }
  }

  private async setInBackendCache<T>(key: string, data: T, ttl: number): Promise<void> {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/cache/${encodeURIComponent(key)}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAccessToken()}`,
        },
        body: JSON.stringify({ data, ttl }),
      });

      if (!response.ok) {
        console.warn(`Backend cache set failed for ${key}: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.warn(`Backend cache set failed for ${key}:`, error);
    }
  }

  private async removeFromBackendCache(key: string): Promise<void> {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/cache/${encodeURIComponent(key)}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.getAccessToken()}`,
        },
      });

      if (!response.ok && response.status !== 404) {
        console.warn(`Backend cache remove failed for ${key}: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.warn(`Backend cache remove failed for ${key}:`, error);
    }
  }

  private async clearBackendCache(): Promise<void> {
    try {
      const response = await fetch('http://localhost:8000/api/v1/cache/clear?confirm=true', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.getAccessToken()}`,
        },
      });

      if (!response.ok) {
        console.warn(`Backend cache clear failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.warn('Backend cache clear failed:', error);
    }
  }

  private isExpired(item: CacheItem<any>): boolean {
    return Date.now() - item.timestamp > item.ttl;
  }

  private getAccessToken(): string | null {
    return localStorage.getItem('access_token');
  }
}

// Export singleton instance
export const hybridCache = new HybridCacheClient();

// Specialized cache for user data
export const userCache = new HybridCacheClient({
  defaultTTL: 10 * 60 * 1000, // 10 minutes for user data
  useBackendCache: true,
  fallbackToLocalStorage: true,
});

// Specialized cache for app data (settings, preferences)
export const appCache = new HybridCacheClient({
  defaultTTL: 60 * 60 * 1000, // 1 hour for app data
  useBackendCache: false, // App data stays local
  fallbackToLocalStorage: true,
});
