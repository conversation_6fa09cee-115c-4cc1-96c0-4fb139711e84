# Cache API Integration Documentation

## Overview

This document describes the Redis cache integration between the FastAPI backend and Next.js 15 frontend, providing a comprehensive multi-layer caching strategy.

## Architecture

### Multi-Layer Caching Strategy

```
Frontend Request
       ↓
1. Memory Cache (Fastest)
       ↓ (miss)
2. localStorage (Fast + Persistent)
       ↓ (miss)
3. Backend Redis via API (Shared + Scalable)
       ↓ (miss)
4. Fresh Data Fetch
```

### Benefits

- **Performance**: Multiple cache layers for optimal speed
- **Security**: Redis stays behind authenticated API endpoints
- **Scalability**: Shared cache across user devices and sessions
- **Reliability**: Graceful fallback between cache layers
- **Consistency**: Coordinated cache invalidation across all layers

## Backend API Endpoints

### Base URL: `/api/v1/cache`

### Authentication
All cache endpoints require JWT authentication via `Authorization: Bearer <token>` header.

### User-Scoped Caching
All cache operations are automatically scoped to the authenticated user using the pattern:
```
user:{user_id}:{cache_key}
```

### Endpoints

#### 1. Get Cache Item
```http
GET /api/v1/cache/{key}
```

**Response:**
```json
{
  "data": "any_json_data",
  "ttl": 300,
  "created_at": "2024-01-01T12:00:00Z"
}
```

#### 2. Set Cache Item
```http
PUT /api/v1/cache/{key}
```

**Request Body:**
```json
{
  "data": "any_json_data",
  "ttl": 300
}
```

**Limits:**
- Maximum data size: 1MB per item
- TTL range: 1 second to 24 hours (86400 seconds)

#### 3. Delete Cache Item
```http
DELETE /api/v1/cache/{key}
```

#### 4. Clear User Cache
```http
DELETE /api/v1/cache/clear?confirm=true
```

#### 5. Bulk Operations
```http
POST /api/v1/cache/bulk
```

**Request Body:**
```json
{
  "items": {
    "key1": {"data": "value1", "ttl": 300},
    "key2": {"data": "value2", "ttl": 600}
  }
}
```

**Limits:**
- Maximum 100 items per bulk operation

#### 6. Cache Key Information
```http
GET /api/v1/cache/info/{key}
```

**Response:**
```json
{
  "key": "user_preferences",
  "exists": true,
  "ttl": 250,
  "size": 1024
}
```

#### 7. Search Cache Keys
```http
POST /api/v1/cache/search
```

**Request Body:**
```json
{
  "pattern": "user_*",
  "limit": 100
}
```

#### 8. Cache Statistics
```http
GET /api/v1/cache/stats
```

**Response:**
```json
{
  "user_id": "123",
  "total_keys": 15,
  "total_size_bytes": 51200,
  "oldest_key_age": 3600,
  "newest_key_age": 30,
  "key_categories": {
    "user": 5,
    "session": 3,
    "preferences": 7
  }
}
```

#### 9. Cache Invalidation
```http
POST /api/v1/cache/invalidate
```

**Request Body:**
```json
{
  "patterns": ["temp_*", "session_*"],
  "confirm": true
}
```

#### 10. Health Check
```http
GET /api/v1/cache/health
```

**Response:**
```json
{
  "status": "healthy",
  "response_time_ms": 2.5,
  "connection": "active"
}
```

### Admin Endpoints

#### Global Cache Statistics (Admin Only)
```http
GET /api/v1/cache/admin/stats
```

## Frontend Integration

### Hybrid Cache Client

The frontend provides a `HybridCacheClient` that automatically manages all cache layers:

```typescript
import { hybridCache, userCache, appCache } from '@/lib/hybrid-cache-client';

// Basic usage
const data = await hybridCache.get('user_preferences', async () => {
  // Fallback function if cache miss
  return await fetchUserPreferences();
});

await hybridCache.set('user_preferences', data, 600); // 10 minutes TTL
```

### Specialized Cache Instances

#### User Cache
```typescript
// For user-specific data with backend sync
const userData = await userCache.get('profile', fetchUserProfile);
```

#### App Cache
```typescript
// For app-specific data (localStorage only)
const appSettings = await appCache.get('settings', fetchAppSettings);
```

### Enhanced API Client

The `EnhancedApiClient` integrates caching with API calls:

```typescript
import { enhancedApiClient } from '@/lib/enhanced-api-client';

// Automatically cached user data
const user = await enhancedApiClient.getCurrentUser();

// Force refresh and update cache
const user = await enhancedApiClient.getCurrentUser(true);
```

## Configuration

### Backend Configuration

In `app/core/config.py`:

```python
# Caching
ENABLE_CACHING: bool = True
CACHE_DEFAULT_TTL: int = 300  # 5 minutes
CACHE_USER_TTL: int = 900     # 15 minutes
CACHE_SESSION_TTL: int = 1800 # 30 minutes

# Redis settings
REDIS_HOST: str = "localhost"
REDIS_PORT: int = 6379
REDIS_DB: int = 0
REDIS_PASSWORD: Optional[str] = None
```

### Frontend Configuration

```typescript
// Default configuration
const hybridCache = new HybridCacheClient({
  defaultTTL: 5 * 60 * 1000,      // 5 minutes
  useBackendCache: true,           // Enable backend Redis
  fallbackToLocalStorage: true,    // Enable localStorage fallback
});
```

## Security Considerations

### Data Validation
- All cache keys are sanitized to prevent injection attacks
- Data size limits prevent abuse (1MB per item)
- TTL limits prevent indefinite storage

### Access Control
- All cache operations are user-scoped
- JWT authentication required for all endpoints
- Admin endpoints require additional permissions

### Key Patterns
```
user:{user_id}:{cache_key}    # User-specific data
admin:{admin_id}:{cache_key}  # Admin-specific data
global:{cache_key}            # Global application data
```

## Performance Optimization

### Cache Hit Rates
- Memory cache: ~1ms response time
- localStorage: ~5ms response time  
- Backend Redis: ~10-50ms response time
- Fresh data: 100ms+ response time

### Best Practices

1. **Use appropriate TTL values**:
   - User data: 10-15 minutes
   - Session data: 30 minutes
   - App settings: 1 hour
   - Static data: 24 hours

2. **Implement cache warming**:
   ```typescript
   // Pre-populate cache on login
   await userCache.set('profile', userProfile);
   ```

3. **Handle cache invalidation**:
   ```typescript
   // Invalidate on data updates
   await userCache.remove('profile');
   ```

4. **Monitor cache performance**:
   ```typescript
   const stats = await fetch('/api/v1/cache/stats');
   ```

## Testing

### Backend Testing
```bash
# Run the integration test
cd backend
python test_cache_integration.py
```

### Frontend Testing
```typescript
// Use the cache demo component
import CacheDemo from '@/components/demo/cache-demo';
```

## Monitoring and Debugging

### Cache Health Monitoring
```typescript
const health = await fetch('/api/v1/cache/health');
```

### Cache Statistics
```typescript
const stats = await fetch('/api/v1/cache/stats');
```

### Debug Logging
Enable debug logging to see cache operations:
```typescript
// Browser console will show:
// "Cache HIT (memory): user_preferences"
// "Cache MISS: Fetching fresh data for user_profile"
```

## Error Handling

The cache system is designed to be resilient:

1. **Cache failures don't break functionality** - falls back to fresh data
2. **Network errors are handled gracefully** - uses local cache layers
3. **Invalid data is rejected** - with proper error messages
4. **Rate limiting is respected** - with exponential backoff

## Migration Guide

### From localStorage Only
```typescript
// Before
const data = JSON.parse(localStorage.getItem('key') || '{}');

// After
const data = await hybridCache.get('key', fetchFreshData);
```

### From Direct API Calls
```typescript
// Before
const user = await fetch('/api/v1/users/me').then(r => r.json());

// After
const user = await enhancedApiClient.getCurrentUser();
```

This cache integration provides a robust, scalable, and secure caching solution that leverages your existing Redis infrastructure while maintaining excellent frontend performance.
