// Type validation utilities to ensure frontend and backend type safety
import type { components } from "@/types/api";
import type { User, AuthTokens, ApiError, SignupData, LoginCredentials } from "@/types";

// OpenAPI types
type OpenAPIUser = components["schemas"]["UserResponse"];
type OpenAPIToken = components["schemas"]["Token"];
type OpenAPIUserCreate = components["schemas"]["UserCreate"];
type OpenAPIValidationError = components["schemas"]["HTTPValidationError"];

// Type compatibility checks
export type TypeCompatibilityReport = {
  compatible: boolean;
  issues: string[];
  recommendations: string[];
};

// Check User type compatibility
export function checkUserTypeCompatibility(): TypeCompatibilityReport {
  const issues: string[] = [];
  const recommendations: string[] = [];

  // Manual User type issues:
  // 1. updated_at is required in manual type but optional in OpenAPI
  // 2. Missing 'role' field from OpenAPI
  // 3. created_at format should be date-time

  issues.push("Manual User.updated_at is required (string) but OpenAPI has optional (string | null)");
  issues.push("Manual User missing 'role' field that exists in OpenAPI UserResponse");
  
  recommendations.push("Update manual User type to match OpenAPI UserResponse");
  recommendations.push("Make updated_at optional: updated_at?: string | null");
  recommendations.push("Add role field: role: string");

  return {
    compatible: false,
    issues,
    recommendations,
  };
}

// Check AuthTokens type compatibility
export function checkAuthTokensCompatibility(): TypeCompatibilityReport {
  const issues: string[] = [];
  const recommendations: string[] = [];

  // AuthTokens matches Token schema perfectly
  return {
    compatible: true,
    issues,
    recommendations: ["AuthTokens type is fully compatible with OpenAPI Token schema ✓"],
  };
}

// Check SignupData type compatibility
export function checkSignupDataCompatibility(): TypeCompatibilityReport {
  const issues: string[] = [];
  const recommendations: string[] = [];

  // SignupData matches UserCreate schema perfectly
  return {
    compatible: true,
    issues,
    recommendations: ["SignupData type is fully compatible with OpenAPI UserCreate schema ✓"],
  };
}

// Check ApiError type compatibility
export function checkApiErrorCompatibility(): TypeCompatibilityReport {
  const issues: string[] = [];
  const recommendations: string[] = [];

  issues.push("Manual ApiError.detail is string but OpenAPI has ValidationError[]");
  issues.push("Manual ApiError has status_code field not present in OpenAPI");
  issues.push("OpenAPI HTTPValidationError structure is different from manual ApiError");

  recommendations.push("Consider using OpenAPI HTTPValidationError type directly");
  recommendations.push("Create separate types for different error scenarios");
  recommendations.push("Use HTTP status codes from response rather than embedded status_code");

  return {
    compatible: false,
    issues,
    recommendations,
  };
}

// Generate comprehensive type safety report
export function generateTypeSafetyReport(): {
  overall: boolean;
  reports: Record<string, TypeCompatibilityReport>;
  summary: string[];
} {
  const reports = {
    User: checkUserTypeCompatibility(),
    AuthTokens: checkAuthTokensCompatibility(),
    SignupData: checkSignupDataCompatibility(),
    ApiError: checkApiErrorCompatibility(),
  };

  const overall = Object.values(reports).every(report => report.compatible);
  
  const summary: string[] = [
    "=== Type Safety Report ===",
    `Overall Compatibility: ${overall ? "✓ PASS" : "✗ ISSUES FOUND"}`,
    "",
    "Compatible Types:",
    ...Object.entries(reports)
      .filter(([_, report]) => report.compatible)
      .map(([name, _]) => `  ✓ ${name}`),
    "",
    "Types with Issues:",
    ...Object.entries(reports)
      .filter(([_, report]) => !report.compatible)
      .map(([name, _]) => `  ✗ ${name}`),
    "",
    "Critical Issues:",
    ...Object.values(reports)
      .flatMap(report => report.issues)
      .map(issue => `  - ${issue}`),
    "",
    "Recommendations:",
    ...Object.values(reports)
      .flatMap(report => report.recommendations)
      .map(rec => `  • ${rec}`),
  ];

  return { overall, reports, summary };
}

// Runtime type validation helpers
export function validateUserResponse(data: unknown): data is OpenAPIUser {
  if (!data || typeof data !== "object") return false;
  
  const user = data as Record<string, unknown>;
  
  return (
    typeof user.id === "string" &&
    typeof user.email === "string" &&
    typeof user.is_active === "boolean" &&
    typeof user.is_verified === "boolean" &&
    typeof user.created_at === "string" &&
    typeof user.role === "string" &&
    (user.full_name === null || typeof user.full_name === "string") &&
    (user.updated_at === null || typeof user.updated_at === "string")
  );
}

export function validateTokenResponse(data: unknown): data is OpenAPIToken {
  if (!data || typeof data !== "object") return false;
  
  const token = data as Record<string, unknown>;
  
  return (
    typeof token.access_token === "string" &&
    typeof token.refresh_token === "string" &&
    typeof token.token_type === "string"
  );
}

export function validateValidationError(data: unknown): data is OpenAPIValidationError {
  if (!data || typeof data !== "object") return false;
  
  const error = data as Record<string, unknown>;
  
  return (
    !error.detail || 
    (Array.isArray(error.detail) && 
     error.detail.every(item => 
       typeof item === "object" &&
       item !== null &&
       Array.isArray((item as any).loc) &&
       typeof (item as any).msg === "string" &&
       typeof (item as any).type === "string"
     ))
  );
}

// Type guard functions for better type safety
export function isOpenAPIUser(data: unknown): data is OpenAPIUser {
  return validateUserResponse(data);
}

export function isOpenAPIToken(data: unknown): data is OpenAPIToken {
  return validateTokenResponse(data);
}

export function isOpenAPIValidationError(data: unknown): data is OpenAPIValidationError {
  return validateValidationError(data);
}

// Development helper to log type safety report
export function logTypeSafetyReport(): void {
  if (process.env.NODE_ENV === "development") {
    const report = generateTypeSafetyReport();
    console.group("🔍 Type Safety Report");
    report.summary.forEach(line => console.log(line));
    console.groupEnd();
  }
}
