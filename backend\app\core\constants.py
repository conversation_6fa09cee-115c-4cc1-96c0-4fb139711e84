"""Application constants."""

# User roles
class UserRole:
    ADMIN = "admin"
    USER = "user"
    MODERATOR = "moderator"

# User permissions
class Permission:
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    ADMIN = "admin"

# Token types
class TokenType:
    ACCESS = "access"
    REFRESH = "refresh"
    EMAIL_VERIFICATION = "email_verification"
    PASSWORD_RESET = "password_reset"

# Email templates
class EmailTemplate:
    VERIFICATION = "verification"
    PASSWORD_RESET = "password_reset"
    WELCOME = "welcome"

# Email subjects
EMAIL_VERIFICATION_SUBJECT = "Verify your email address"
PASSWORD_RESET_SUBJECT = "Reset your password"
WELCOME_SUBJECT = "Welcome to our platform"

# HTTP status messages
class StatusMessage:
    SUCCESS = "Success"
    CREATED = "Created successfully"
    UPDATED = "Updated successfully"
    DELETED = "Deleted successfully"
    NOT_FOUND = "Resource not found"
    UNAUTHORIZED = "Unauthorized access"
    FORBIDDEN = "Access forbidden"
    VALIDATION_ERROR = "Validation error"
    INTERNAL_ERROR = "Internal server error"

# Default values
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100

# Legacy constants for backward compatibility
USER_ROLE_ADMIN = UserRole.ADMIN
USER_ROLE_USER = UserRole.USER
TOKEN_TYPE_ACCESS = TokenType.ACCESS
TOKEN_TYPE_REFRESH = TokenType.REFRESH
TOKEN_TYPE_EMAIL_VERIFICATION = TokenType.EMAIL_VERIFICATION