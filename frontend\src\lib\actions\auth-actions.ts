"use server";

import { redirect } from "next/navigation";
import { cookies } from "next/headers";
import { apiClient, apiUtils } from "@/lib/api-client";
import { apiEndpoints, config } from "@/lib/config";
import {
  loginSchema,
  signupSchema,
  passwordResetRequestSchema,
  passwordResetSchema,
  type LoginFormData,
  type SignupFormData
} from "@/lib/validations";
import type { FormState, AuthTokens } from "@/types";

// Login action
export async function loginAction(
  prevState: FormState,
  formData: FormData
): Promise<FormState> {
  try {
    // Extract and validate form data
    const rawData = {
      email: formData.get("email") as string,
      password: formData.get("password") as string,
    };

    const validatedData = loginSchema.parse(rawData);

    // Make API call to login endpoint
    const response = await fetch(apiEndpoints.auth.login, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        username: validatedData.email, // FastAPI expects 'username' field
        password: validatedData.password,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        success: false,
        message: errorData.detail || "Login failed",
        errors: {},
      };
    }

    const tokens: AuthTokens = await response.json();

    // Set session cookie for SSR compatibility
    const cookieStore = await cookies();
    cookieStore.set(config.AUTH_COOKIE_NAME, tokens.access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: 60 * 60 * 24 * 7, // 7 days
    });

    // Note: Client-side token storage will be handled by the login form component
    // after successful server action completion

    return {
      success: true,
      message: "Login successful",
      tokens, // Pass tokens to client for localStorage storage
    };
  } catch (error: any) {
    if (error.name === "ZodError") {
      const fieldErrors: Record<string, string[]> = {};
      error.errors.forEach((err: any) => {
        const field = err.path[0];
        if (!fieldErrors[field]) {
          fieldErrors[field] = [];
        }
        fieldErrors[field].push(err.message);
      });

      return {
        success: false,
        message: "Validation failed",
        errors: fieldErrors,
      };
    }

    return {
      success: false,
      message: error.message || "An unexpected error occurred",
      errors: {},
    };
  }
}

// Signup action
export async function signupAction(
  prevState: FormState,
  formData: FormData
): Promise<FormState> {
  try {
    // Extract and validate form data
    const rawData = {
      email: formData.get("email") as string,
      password: formData.get("password") as string,
      confirmPassword: formData.get("confirmPassword") as string,
      full_name: formData.get("full_name") as string || undefined,
    };

    const validatedData = signupSchema.parse(rawData);

    // Make API call to register endpoint
    const response = await fetch(apiEndpoints.auth.register, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email: validatedData.email,
        password: validatedData.password,
        full_name: validatedData.full_name,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        success: false,
        message: errorData.detail || "Registration failed",
        errors: {},
      };
    }

    return {
      success: true,
      message: "Registration successful! Please check your email to verify your account.",
    };
  } catch (error: any) {
    if (error.name === "ZodError" && error.errors && Array.isArray(error.errors)) {
      const fieldErrors: Record<string, string[]> = {};
      error.errors.forEach((err: any) => {
        const field = err.path[0];
        if (!fieldErrors[field]) {
          fieldErrors[field] = [];
        }
        fieldErrors[field].push(err.message);
      });

      return {
        success: false,
        message: "Validation failed",
        errors: fieldErrors,
      };
    }

    return {
      success: false,
      message: error.message || "An unexpected error occurred",
      errors: {},
    };
  }
}

// Logout action
export async function logoutAction(): Promise<void> {
  try {
    // Clear session cookie
    const cookieStore = await cookies();
    cookieStore.delete(config.AUTH_COOKIE_NAME);

    // Try to call logout endpoint (optional, as cookie is already cleared)
    try {
      await fetch(apiEndpoints.auth.logout, {
        method: "POST",
        credentials: "include",
      });
    } catch (error) {
      // Ignore logout API errors as cookie is already cleared
      console.warn("Logout API call failed:", error);
    }
  } catch (error) {
    console.error("Logout action failed:", error);
  }

  // Redirect to login page
  redirect("/login");
}

// Forgot password action
export async function forgotPasswordAction(
  prevState: FormState,
  formData: FormData
): Promise<FormState> {
  try {
    // Extract and validate form data
    const rawData = {
      email: formData.get("email") as string,
    };

    const validatedData = passwordResetRequestSchema.parse(rawData);

    // Make API call to forgot password endpoint with email as query parameter
    const url = new URL(apiEndpoints.auth.forgotPassword);
    url.searchParams.append('email', validatedData.email);

    const response = await fetch(url.toString(), {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (response.ok) {
      return {
        success: true,
        message: data.message || "Password reset email sent successfully",
        errors: {},
      };
    } else {
      return {
        success: false,
        message: data.detail || "Failed to send password reset email",
        errors: {},
      };
    }
  } catch (error: any) {
    console.error("Forgot password action failed:", error);

    // Handle Zod validation errors
    if (error.name === "ZodError" && error.errors && Array.isArray(error.errors)) {
      return {
        success: false,
        message: "Please check your input and try again.",
        errors: error.errors,
      };
    }

    return {
      success: false,
      message: "An unexpected error occurred. Please try again.",
      errors: {},
    };
  }
}

// Reset password action
export async function resetPasswordAction(
  prevState: FormState,
  formData: FormData
): Promise<FormState> {
  try {
    // Extract and validate form data
    const rawData = {
      token: formData.get("token") as string,
      password: formData.get("password") as string,
      confirmPassword: formData.get("confirmPassword") as string,
    };

    const validatedData = passwordResetSchema.parse(rawData);

    // Make API call to reset password endpoint with parameters as query parameters
    const url = new URL(apiEndpoints.auth.resetPassword);
    url.searchParams.append('token', validatedData.token);
    url.searchParams.append('new_password', validatedData.password);

    const response = await fetch(url.toString(), {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (response.ok) {
      return {
        success: true,
        message: data.message || "Password reset successfully",
        errors: {},
      };
    } else {
      return {
        success: false,
        message: data.detail || "Failed to reset password",
        errors: {},
      };
    }
  } catch (error: any) {
    console.error("Reset password action failed:", error);

    // Handle Zod validation errors
    if (error.name === "ZodError" && error.errors && Array.isArray(error.errors)) {
      return {
        success: false,
        message: "Please check your input and try again.",
        errors: error.errors,
      };
    }

    return {
      success: false,
      message: "An unexpected error occurred. Please try again.",
      errors: {},
    };
  }
}
