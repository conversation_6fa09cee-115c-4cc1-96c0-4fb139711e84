"""Standard API response schemas."""
from typing import Any, Dict, List, Optional, Generic, TypeVar
from pydantic import BaseModel, Field

T = TypeVar('T')


class BaseResponse(BaseModel):
    """Base response model for all API responses."""
    
    success: bool = Field(description="Whether the request was successful")
    message: str = Field(description="Human-readable message")
    timestamp: Optional[str] = Field(None, description="Response timestamp")


class SuccessResponse(BaseResponse, Generic[T]):
    """Success response with data."""
    
    success: bool = Field(True, description="Always true for success responses")
    data: T = Field(description="Response data")


class ErrorResponse(BaseResponse):
    """Error response model."""
    
    success: bool = Field(False, description="Always false for error responses")
    error_code: Optional[str] = Field(None, description="Machine-readable error code")
    errors: Optional[List[Dict[str, Any]]] = Field(None, description="Detailed error information")


class PaginationMeta(BaseModel):
    """Pagination metadata."""
    
    page: int = Field(description="Current page number")
    per_page: int = Field(description="Items per page")
    total: int = Field(description="Total number of items")
    pages: int = Field(description="Total number of pages")
    has_next: bool = Field(description="Whether there is a next page")
    has_prev: bool = Field(description="Whether there is a previous page")


class PaginatedResponse(BaseResponse, Generic[T]):
    """Paginated response model."""
    
    success: bool = Field(True, description="Always true for success responses")
    data: List[T] = Field(description="List of items")
    meta: PaginationMeta = Field(description="Pagination metadata")


class HealthResponse(BaseModel):
    """Health check response model."""
    
    status: str = Field(description="Health status (healthy, warning, unhealthy)")
    version: str = Field(description="Application version")
    environment: str = Field(description="Environment name")
    timestamp: str = Field(description="Health check timestamp")
    checks: Optional[Dict[str, Any]] = Field(None, description="Detailed health checks")


class PerformanceResponse(BaseModel):
    """Performance statistics response model."""
    
    status: str = Field(description="Performance status")
    issues: List[str] = Field(description="Performance issues")
    stats: Dict[str, Any] = Field(description="Performance statistics")


class ValidationErrorDetail(BaseModel):
    """Validation error detail."""
    
    field: str = Field(description="Field name that failed validation")
    message: str = Field(description="Validation error message")
    value: Any = Field(description="Invalid value")


class APIResponse:
    """Utility class for creating standardized API responses."""
    
    @staticmethod
    def success(data: Any = None, message: str = "Success") -> Dict[str, Any]:
        """Create a success response."""
        response = {
            "success": True,
            "message": message,
        }
        
        if data is not None:
            response["data"] = data
        
        return response
    
    @staticmethod
    def error(
        message: str,
        error_code: Optional[str] = None,
        errors: Optional[List[Dict[str, Any]]] = None,
        status_code: int = 400
    ) -> Dict[str, Any]:
        """Create an error response."""
        response = {
            "success": False,
            "message": message,
        }
        
        if error_code:
            response["error_code"] = error_code
        
        if errors:
            response["errors"] = errors
        
        return response
    
    @staticmethod
    def paginated(
        data: List[Any],
        page: int,
        per_page: int,
        total: int,
        message: str = "Success"
    ) -> Dict[str, Any]:
        """Create a paginated response."""
        pages = (total + per_page - 1) // per_page  # Ceiling division
        
        return {
            "success": True,
            "message": message,
            "data": data,
            "meta": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "pages": pages,
                "has_next": page < pages,
                "has_prev": page > 1,
            }
        }
    
    @staticmethod
    def validation_error(errors: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create a validation error response."""
        return {
            "success": False,
            "message": "Validation failed",
            "error_code": "VALIDATION_ERROR",
            "errors": errors,
        }


class MessageResponse(BaseModel):
    """Simple message response."""
    
    message: str = Field(description="Response message")


class TokenResponse(BaseModel):
    """Token response model."""
    
    access_token: str = Field(description="JWT access token")
    refresh_token: str = Field(description="JWT refresh token")
    token_type: str = Field("bearer", description="Token type")
    expires_in: int = Field(description="Token expiration time in seconds")


class StatusResponse(BaseModel):
    """Status response model."""
    
    status: str = Field(description="Operation status")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional details")


# Common response examples for OpenAPI documentation
RESPONSE_EXAMPLES = {
    "success": {
        "summary": "Success response",
        "value": {
            "success": True,
            "message": "Operation completed successfully",
            "data": {"id": 1, "name": "Example"}
        }
    },
    "error": {
        "summary": "Error response",
        "value": {
            "success": False,
            "message": "An error occurred",
            "error_code": "VALIDATION_ERROR",
            "errors": [
                {
                    "field": "email",
                    "message": "Invalid email format",
                    "value": "invalid-email"
                }
            ]
        }
    },
    "paginated": {
        "summary": "Paginated response",
        "value": {
            "success": True,
            "message": "Data retrieved successfully",
            "data": [
                {"id": 1, "name": "Item 1"},
                {"id": 2, "name": "Item 2"}
            ],
            "meta": {
                "page": 1,
                "per_page": 10,
                "total": 25,
                "pages": 3,
                "has_next": True,
                "has_prev": False
            }
        }
    },
    "unauthorized": {
        "summary": "Unauthorized response",
        "value": {
            "success": False,
            "message": "Authentication required",
            "error_code": "AUTH_REQUIRED"
        }
    },
    "forbidden": {
        "summary": "Forbidden response",
        "value": {
            "success": False,
            "message": "Insufficient permissions",
            "error_code": "INSUFFICIENT_PERMISSIONS"
        }
    },
    "not_found": {
        "summary": "Not found response",
        "value": {
            "success": False,
            "message": "Resource not found",
            "error_code": "NOT_FOUND"
        }
    },
    "rate_limited": {
        "summary": "Rate limited response",
        "value": {
            "success": False,
            "message": "Rate limit exceeded",
            "error_code": "RATE_LIMIT_EXCEEDED"
        }
    }
}
