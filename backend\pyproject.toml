[project]
name = "backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "alembic>=1.16.4",
    "asyncpg>=0.30.0",
    "fastapi[standard]>=0.116.1",
    "psycopg2>=2.9.10",
    "pytest>=8.4.1",
    "resend>=2.11.0",
    "sqlalchemy>=2.0.42",
    "uvicorn[standard]>=0.35.0",
    # Authentication & Security
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    # Rate limiting
    "slowapi>=0.1.9",
    # Security headers
    "secure>=0.3.0",
    # Email validation
    "email-validator>=2.0.0",
    # Testing
    "httpx>=0.25.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pydantic-settings>=2.10.1",
    "python-json-logger>=3.3.0",
    "psutil>=7.0.0",
    "redis>=6.2.0",
    "aioredis>=2.0.1",
]

[dependency-groups]
dev = [
    "aiosqlite>=0.21.0",
]
