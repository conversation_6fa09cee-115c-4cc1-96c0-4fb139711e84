"""Redis-based caching utilities for performance optimization."""

import json
import pickle
from typing import Any, Optional, Union, Dict, List
from datetime import datetime, timedelta
from functools import wraps
import hashlib
import asyncio
from contextlib import asynccontextmanager

import aioredis
from aioredis import Redis

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class RedisCache:
    """Redis-based cache implementation."""

    def __init__(self, redis_client: Optional[Redis] = None):
        self._redis: Optional[Redis] = redis_client
        self._connection_pool = None

    async def _get_redis(self) -> Redis:
        """Get Redis client, creating connection if needed."""
        if self._redis is None:
            try:
                self._redis = aioredis.from_url(
                    settings.redis_url,
                    decode_responses=True,
                    retry_on_timeout=True,
                    socket_keepalive=True,
                    socket_keepalive_options={},
                    health_check_interval=30,
                )
                # Test connection
                await self._redis.ping()
                logger.info("Redis connection established successfully")
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {e}")
                raise
        return self._redis

    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            redis = await self._get_redis()
            value = await redis.get(key)
            if value is not None:
                logger.debug(f"Cache hit for key: {key}")
                try:
                    # Try to deserialize as JSON first
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    # If JSON fails, try pickle
                    try:
                        import base64

                        return pickle.loads(base64.b64decode(value.encode()))
                    except Exception:
                        # Return as string if all else fails
                        return value

            logger.debug(f"Cache miss for key: {key}")
            return None
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return None

    async def set(self, key: str, value: Any, ttl: int = 300) -> None:
        """Set value in cache with TTL in seconds."""
        try:
            redis = await self._get_redis()

            # Serialize value
            if isinstance(value, (str, int, float, bool)):
                serialized_value = json.dumps(value)
            elif isinstance(value, (dict, list)):
                serialized_value = json.dumps(value)
            else:
                # Use pickle for complex objects
                import base64

                serialized_value = base64.b64encode(pickle.dumps(value)).decode()

            await redis.setex(key, ttl, serialized_value)
            logger.debug(f"Cache set for key: {key} with TTL: {ttl}s")
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")

    async def delete(self, key: str) -> bool:
        """Delete key from cache."""
        try:
            redis = await self._get_redis()
            result = await redis.delete(key)
            logger.debug(f"Cache delete for key: {key}")
            return bool(result)
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False

    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        try:
            redis = await self._get_redis()
            result = await redis.exists(key)
            return bool(result)
        except Exception as e:
            logger.error(f"Cache exists error for key {key}: {e}")
            return False

    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern."""
        try:
            redis = await self._get_redis()
            keys = await redis.keys(pattern)
            if keys:
                deleted = await redis.delete(*keys)
                logger.info(f"Cleared {deleted} keys matching pattern: {pattern}")
                return deleted
            return 0
        except Exception as e:
            logger.error(f"Cache clear pattern error for pattern {pattern}: {e}")
            return 0

    async def clear_all(self) -> bool:
        """Clear all cache entries."""
        try:
            redis = await self._get_redis()
            await redis.flushdb()
            logger.info("All cache entries cleared")
            return True
        except Exception as e:
            logger.error(f"Cache clear all error: {e}")
            return False

    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            redis = await self._get_redis()
            info = await redis.info()

            return {
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory", 0),
                "used_memory_human": info.get("used_memory_human", "0B"),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "total_commands_processed": info.get("total_commands_processed", 0),
                "redis_version": info.get("redis_version", "unknown"),
                "uptime_in_seconds": info.get("uptime_in_seconds", 0),
            }
        except Exception as e:
            logger.error(f"Cache stats error: {e}")
            return {}

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on Redis."""
        try:
            redis = await self._get_redis()
            start_time = datetime.utcnow()
            await redis.ping()
            response_time = (datetime.utcnow() - start_time).total_seconds() * 1000

            return {
                "status": "healthy",
                "response_time_ms": round(response_time, 2),
                "connection": "active",
            }
        except Exception as e:
            return {"status": "unhealthy", "error": str(e), "connection": "failed"}

    async def ttl(self, key: str) -> Optional[int]:
        """Get time to live for a key in seconds."""
        try:
            redis = await self._get_redis()
            ttl_value = await redis.ttl(key)
            # Redis returns -1 if key exists but has no TTL, -2 if key doesn't exist
            return ttl_value if ttl_value > 0 else None
        except Exception as e:
            logger.error(f"Cache TTL error for key {key}: {e}")
            return None

    async def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching a pattern."""
        try:
            redis = await self._get_redis()
            keys = await redis.keys(pattern)
            if keys:
                deleted = await redis.delete(*keys)
                logger.debug(f"Deleted {deleted} keys matching pattern: {pattern}")
                return deleted
            return 0
        except Exception as e:
            logger.error(f"Cache delete pattern error for pattern {pattern}: {e}")
            return 0

    async def keys(self, pattern: str = "*") -> List[str]:
        """Get all keys matching a pattern."""
        try:
            redis = await self._get_redis()
            keys = await redis.keys(pattern)
            return keys
        except Exception as e:
            logger.error(f"Cache keys error for pattern {pattern}: {e}")
            return []

    async def close(self):
        """Close Redis connection."""
        if self._redis:
            try:
                await self._redis.close()
                logger.info("Redis connection closed")
            except Exception as e:
                logger.error(f"Error closing Redis connection: {e}")
            finally:
                self._redis = None


# Global cache instance
cache = RedisCache()


def cache_key(*args, **kwargs) -> str:
    """Generate cache key from arguments."""
    # Create a string representation of all arguments
    key_data = {"args": args, "kwargs": sorted(kwargs.items())}
    key_string = json.dumps(key_data, sort_keys=True, default=str)

    # Create hash for consistent key length
    return hashlib.md5(key_string.encode()).hexdigest()


def cached(ttl: int = 300, key_prefix: str = "", skip_cache: bool = False):
    """Decorator for caching function results."""

    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            if skip_cache or not settings.ENABLE_CACHING:
                return await func(*args, **kwargs)

            # Generate cache key
            func_name = f"{func.__module__}.{func.__name__}"
            key = f"{key_prefix}:{func_name}:{cache_key(*args, **kwargs)}"

            # Try to get from cache
            cached_result = await cache.get(key)
            if cached_result is not None:
                return cached_result

            # Execute function and cache result
            try:
                result = await func(*args, **kwargs)
                await cache.set(key, result, ttl)
                return result
            except Exception as e:
                logger.error(f"Error in cached function {func_name}: {e}")
                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            if skip_cache or not settings.ENABLE_CACHING:
                return func(*args, **kwargs)

            # For sync functions, we'll use a simple approach
            # In production, you might want to use Redis or similar
            func_name = f"{func.__module__}.{func.__name__}"
            key = f"{key_prefix}:{func_name}:{cache_key(*args, **kwargs)}"

            # This is a simplified sync version
            # In practice, you'd want proper async handling
            return func(*args, **kwargs)

        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


class CacheManager:
    """Cache management utilities."""

    @staticmethod
    async def invalidate_pattern(pattern: str) -> int:
        """Invalidate cache keys matching pattern."""
        try:
            count = await cache.clear_pattern(pattern)
            logger.info(f"Invalidated cache pattern '{pattern}': {count} entries")
            return count
        except Exception as e:
            logger.error(f"Failed to invalidate pattern '{pattern}': {e}")
            return 0

    @staticmethod
    async def warm_cache():
        """Warm up cache with frequently accessed data."""
        logger.info("Starting cache warm-up")

        try:
            # Add cache warming logic here
            # For example, pre-load user permissions, settings, etc.

            # Example: Cache application settings
            from app.core.config import settings as app_settings

            await cache.set("app:settings:version", app_settings.VERSION, ttl=3600)

            logger.info("Cache warm-up completed")

        except Exception as e:
            logger.error(f"Cache warm-up failed: {e}")

    @staticmethod
    async def health_check() -> Dict[str, Any]:
        """Check cache health."""
        try:
            # Test cache operations
            test_key = "health:check"
            test_value = {"timestamp": datetime.utcnow().isoformat()}

            await cache.set(test_key, test_value, ttl=60)
            retrieved = await cache.get(test_key)
            await cache.delete(test_key)

            stats = await cache.get_stats()

            return {
                "status": "healthy" if retrieved == test_value else "unhealthy",
                "stats": stats,
                "test_passed": retrieved == test_value,
            }

        except Exception as e:
            logger.error(f"Cache health check failed: {e}")
            return {"status": "unhealthy", "error": str(e), "test_passed": False}


# Cache maintenance task
async def cache_maintenance_task():
    """Background task for cache maintenance and health monitoring."""
    while True:
        try:
            # Perform health check
            health = await cache.health_check()
            if health.get("status") != "healthy":
                logger.warning(f"Cache health check failed: {health}")

            # Log cache statistics
            stats = await cache.get_stats()
            logger.debug(f"Cache stats: {stats}")

            await asyncio.sleep(300)  # Run every 5 minutes
        except Exception as e:
            logger.error(f"Cache maintenance task error: {e}")
            await asyncio.sleep(60)  # Retry after 1 minute on error


# Context manager for cache operations
@asynccontextmanager
async def cache_context():
    """Context manager for cache operations."""
    try:
        yield cache
    except Exception as e:
        logger.error(f"Cache context error: {e}")
        raise


# Common cache patterns
class CachePatterns:
    """Common caching patterns and utilities."""

    USER_PREFIX = "user"
    SESSION_PREFIX = "session"
    PERMISSION_PREFIX = "permission"
    SETTINGS_PREFIX = "settings"

    @staticmethod
    def user_key(user_id: str) -> str:
        """Generate user cache key."""
        return f"{CachePatterns.USER_PREFIX}:{user_id}"

    @staticmethod
    def session_key(session_token: str) -> str:
        """Generate session cache key."""
        return f"{CachePatterns.SESSION_PREFIX}:{session_token}"

    @staticmethod
    def permission_key(user_id: str, resource: str) -> str:
        """Generate permission cache key."""
        return f"{CachePatterns.PERMISSION_PREFIX}:{user_id}:{resource}"

    @staticmethod
    async def invalidate_user_cache(user_id: str):
        """Invalidate all cache entries for a user."""
        keys_to_delete = [
            CachePatterns.user_key(user_id),
            f"{CachePatterns.PERMISSION_PREFIX}:{user_id}:*",
        ]

        for key in keys_to_delete:
            if "*" in key:
                # Pattern invalidation
                await CacheManager.invalidate_pattern(key)
            else:
                await cache.delete(key)

        logger.info(f"Invalidated cache for user: {user_id}")
