// Tests for type validation and OpenAPI type checking
import {
  generateTypeSafetyReport,
  validateUserResponse,
  validateTokenResponse,
  validateValidationError,
  isOpenAPIUser,
  isOpenAPIToken,
  isOpenAPIValidationError,
} from "../type-validation";

describe("Type Validation", () => {
  describe("generateTypeSafetyReport", () => {
    it("should generate a comprehensive type safety report", () => {
      const report = generateTypeSafetyReport();

      expect(report).toHaveProperty("overall");
      expect(report).toHaveProperty("reports");
      expect(report).toHaveProperty("summary");

      expect(typeof report.overall).toBe("boolean");
      expect(Array.isArray(report.summary)).toBe(true);

      // Should have reports for all checked types
      expect(report.reports).toHaveProperty("User");
      expect(report.reports).toHaveProperty("AuthTokens");
      expect(report.reports).toHaveProperty("SignupData");
      expect(report.reports).toHaveProperty("ApiError");
    });

    it("should identify type compatibility issues", () => {
      const report = generateTypeSafetyReport();

      // User type should have compatibility issues
      expect(report.reports.User.compatible).toBe(false);
      expect(report.reports.User.issues.length).toBeGreaterThan(0);

      // AuthTokens should be compatible
      expect(report.reports.AuthTokens.compatible).toBe(true);

      // SignupData should be compatible
      expect(report.reports.SignupData.compatible).toBe(true);

      // ApiError should have compatibility issues
      expect(report.reports.ApiError.compatible).toBe(false);
    });
  });

  describe("validateUserResponse", () => {
    it("should validate correct user response", () => {
      const validUser = {
        id: "123e4567-e89b-12d3-a456-426614174000",
        email: "<EMAIL>",
        full_name: "Test User",
        is_active: true,
        is_verified: true,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        role: "user",
      };

      expect(validateUserResponse(validUser)).toBe(true);
      expect(isOpenAPIUser(validUser)).toBe(true);
    });

    it("should reject invalid user response", () => {
      const invalidUser = {
        id: "123",
        email: "<EMAIL>",
        // missing required fields
      };

      expect(validateUserResponse(invalidUser)).toBe(false);
      expect(isOpenAPIUser(invalidUser)).toBe(false);
    });

    it("should handle null values correctly", () => {
      const userWithNulls = {
        id: "123e4567-e89b-12d3-a456-426614174000",
        email: "<EMAIL>",
        full_name: null,
        is_active: true,
        is_verified: false,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: null,
        role: "user",
      };

      expect(validateUserResponse(userWithNulls)).toBe(true);
    });
  });

  describe("validateTokenResponse", () => {
    it("should validate correct token response", () => {
      const validToken = {
        access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        refresh_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        token_type: "bearer",
      };

      expect(validateTokenResponse(validToken)).toBe(true);
      expect(isOpenAPIToken(validToken)).toBe(true);
    });

    it("should reject invalid token response", () => {
      const invalidToken = {
        access_token: "token",
        // missing refresh_token and token_type
      };

      expect(validateTokenResponse(invalidToken)).toBe(false);
      expect(isOpenAPIToken(invalidToken)).toBe(false);
    });
  });

  describe("validateValidationError", () => {
    it("should validate correct validation error", () => {
      const validError = {
        detail: [
          {
            loc: ["body", "email"],
            msg: "field required",
            type: "value_error.missing",
          },
        ],
      };

      expect(validateValidationError(validError)).toBe(true);
      expect(isOpenAPIValidationError(validError)).toBe(true);
    });

    it("should handle empty detail array", () => {
      const emptyError = {
        detail: [],
      };

      expect(validateValidationError(emptyError)).toBe(true);
    });

    it("should handle missing detail field", () => {
      const noDetailError = {};

      expect(validateValidationError(noDetailError)).toBe(true);
    });

    it("should reject invalid validation error", () => {
      const invalidError = {
        detail: [
          {
            loc: "invalid", // should be array
            msg: "error",
            type: "error",
          },
        ],
      };

      expect(validateValidationError(invalidError)).toBe(false);
    });
  });

  describe("Type Safety Integration", () => {
    it("should detect type mismatches between manual and OpenAPI types", () => {
      const report = generateTypeSafetyReport();

      // Should detect User type issues
      const userIssues = report.reports.User.issues;
      expect(userIssues.some(issue => issue.includes("updated_at"))).toBe(true);
      expect(userIssues.some(issue => issue.includes("role"))).toBe(true);

      // Should detect ApiError type issues
      const apiErrorIssues = report.reports.ApiError.issues;
      expect(apiErrorIssues.some(issue => issue.includes("detail"))).toBe(true);
      expect(apiErrorIssues.some(issue => issue.includes("status_code"))).toBe(true);
    });

    it("should provide actionable recommendations", () => {
      const report = generateTypeSafetyReport();

      const userRecommendations = report.reports.User.recommendations;
      expect(userRecommendations.some(rec => rec.includes("updated_at"))).toBe(true);
      expect(userRecommendations.some(rec => rec.includes("role"))).toBe(true);
    });
  });
});

// Integration test with actual API types
describe("OpenAPI Type Integration", () => {
  it("should validate type structure at compile time", () => {
    // This test validates that the types have the expected structure
    // It will fail at compile time if the types are incorrect

    // Test authentication endpoint types exist
    const authEndpoints = [
      "/api/v1/auth/register",
      "/api/v1/auth/login",
      "/api/v1/auth/refresh",
      "/api/v1/auth/verify-email",
      "/api/v1/auth/forgot-password",
      "/api/v1/auth/reset-password"
    ];

    // Test user endpoint types exist
    const userEndpoints = [
      "/api/v1/users/me",
      "/api/v1/users/",
      "/api/v1/users/{user_id}"
    ];

    // Test schema component types exist
    const schemaComponents = [
      "UserResponse",
      "UserCreate",
      "UserUpdate",
      "Token",
      "HTTPValidationError",
      "ValidationError"
    ];

    // If we can reference these without TypeScript errors, the types exist
    expect(authEndpoints.length).toBeGreaterThan(0);
    expect(userEndpoints.length).toBeGreaterThan(0);
    expect(schemaComponents.length).toBeGreaterThan(0);
  });
});
