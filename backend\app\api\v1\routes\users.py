"""User management routes."""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.api.deps import (
    get_async_db,
    get_current_user,
    get_admin_user,
    require_admin_permission
)
from app.models.user import User
from app.schemas.user import UserResponse, UserUpdate
from app.crud.user import user_crud
from app.core.permissions import require_permissions, require_roles
from app.core.constants import Permission, UserRole

router = APIRouter(prefix="/users", tags=["User Management"])


@router.get(
    "/me",
    response_model=UserResponse,
    summary="Get current user profile",
    description="Retrieve the authenticated user's profile information"
)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    Get current user's profile information.

    Returns the authenticated user's profile data including:
    - Basic information (name, email)
    - Account status (verified, active)
    - Role and permissions
    """
    return current_user


@router.put(
    "/me",
    response_model=UserResponse,
    summary="Update current user profile",
    description="Update the authenticated user's profile information"
)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """
    Update current user's profile information.

    Allows updating:
    - Full name
    - Bio
    - Other profile fields

    Note: Email changes require verification.
    """
    updated_user = await user_crud.update(db, db_obj=current_user, obj_in=user_update)
    return updated_user


@router.get(
    "/",
    response_model=List[UserResponse],
    summary="List all users",
    description="Get paginated list of all users (admin only)"
)
@require_permissions(Permission.ADMIN)
async def get_users(
    skip: int = Query(0, ge=0, description="Number of users to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of users to return"),
    current_user: User = Depends(get_admin_user),
    db: AsyncSession = Depends(get_async_db)
):
    """
    Get paginated list of all users.

    **Admin only endpoint**

    - **skip**: Number of users to skip (for pagination)
    - **limit**: Maximum number of users to return (1-100)

    Returns list of user profiles with pagination support.
    """
    result = await db.execute(
        select(User).offset(skip).limit(limit)
    )
    users = result.scalars().all()
    return users


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get user by ID."""
    # Users can view their own profile, admins can view any profile
    if str(current_user.id) != user_id and not current_user.is_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )

    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return user


@router.put("/{user_id}", response_model=UserResponse)
@require_roles(UserRole.ADMIN)
async def update_user(
    user_id: str,
    user_update: UserUpdate,
    current_user: User = Depends(get_admin_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Update user by ID (admin only)."""
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    updated_user = await user_crud.update(db, db_obj=user, obj_in=user_update)
    return updated_user


@router.delete("/{user_id}")
@require_permissions(Permission.ADMIN)
async def delete_user(
    user_id: str,
    current_user: User = Depends(get_admin_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Delete user by ID (admin only)."""
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    if str(user.id) == str(current_user.id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own account"
        )

    await user_crud.remove(db, id=user_id)
    return {"message": "User deleted successfully"}