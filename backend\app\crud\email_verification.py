"""Email verification CRUD operations."""

import uuid
from typing import Optional, Union
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from app.models.email_verification import EmailVerification
from app.core.security import generate_verification_token
from app.core.config import settings
from app.core.constants import TokenType


class CRUDEmailVerification:
    async def create_verification(
        self,
        db: AsyncSession,
        *,
        user_id: Union[str, uuid.UUID],
        email: str,
        token_type: str = TokenType.EMAIL_VERIFICATION,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> EmailVerification:
        """Create a new email verification token."""
        token = generate_verification_token()

        # Set expiration based on token type
        if token_type == TokenType.PASSWORD_RESET:
            expires_at = datetime.utcnow() + timedelta(
                hours=settings.PASSWORD_RESET_EXPIRE_HOURS
            )
        else:
            expires_at = datetime.utcnow() + timedelta(
                hours=settings.EMAIL_VERIFICATION_EXPIRE_HOURS
            )

        # Convert user_id to UUID if it's a string
        if isinstance(user_id, str):
            user_id = uuid.UUID(user_id)

        db_obj = EmailVerification(
            user_id=user_id,
            email=email,
            token=token,
            token_type=token_type,
            expires_at=expires_at,
            ip_address=ip_address,
            user_agent=user_agent,
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_by_token(
        self, db: AsyncSession, *, token: str
    ) -> Optional[EmailVerification]:
        """Get verification by token."""
        result = await db.execute(
            select(EmailVerification).where(EmailVerification.token == token)
        )
        return result.scalar_one_or_none()

    async def get_by_user_and_type(
        self,
        db: AsyncSession,
        *,
        user_id: str,
        token_type: str,
        valid_only: bool = True,
    ) -> Optional[EmailVerification]:
        """Get verification by user and token type."""
        query = select(EmailVerification).where(
            EmailVerification.user_id == user_id,
            EmailVerification.token_type == token_type,
        )

        if valid_only:
            query = query.where(
                EmailVerification.is_used == False,
                EmailVerification.expires_at > datetime.utcnow(),
            )

        result = await db.execute(query.order_by(EmailVerification.created_at.desc()))
        return result.scalar_one_or_none()

    async def mark_as_used(self, db: AsyncSession, *, verification_id: str) -> bool:
        """Mark verification as used."""
        result = await db.execute(
            update(EmailVerification)
            .where(EmailVerification.id == verification_id)
            .values(is_used=True, verified_at=datetime.utcnow())
        )
        await db.commit()
        return result.rowcount > 0

    async def invalidate_user_verifications(
        self, db: AsyncSession, *, user_id: str, token_type: str
    ) -> int:
        """Invalidate all verifications of a type for a user."""
        result = await db.execute(
            update(EmailVerification)
            .where(
                EmailVerification.user_id == user_id,
                EmailVerification.token_type == token_type,
                EmailVerification.is_used == False,
            )
            .values(is_used=True)
        )
        await db.commit()
        return result.rowcount


email_verification_crud = CRUDEmailVerification()
