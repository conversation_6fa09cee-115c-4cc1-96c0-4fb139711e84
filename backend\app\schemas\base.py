"""Base schemas."""

from pydantic import BaseModel, field_validator
from datetime import datetime
from typing import Optional, Union
import uuid


class BaseSchema(BaseModel):
    id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    @field_validator("id", mode="before")
    @classmethod
    def validate_id(cls, v: Union[str, uuid.UUID]) -> str:
        """Convert UUID to string if necessary."""
        if isinstance(v, uuid.UUID):
            return str(v)
        return v

    class Config:
        from_attributes = True
