"""Application configuration using Pydantic BaseSettings."""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import field_validator, ConfigDict, Field


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    model_config = ConfigDict(env_file=".env")

    PROJECT_NAME: str = "FastAPI Backend"
    API_V1_STR: str = "/api/v1"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "A production-ready FastAPI backend with authentication"

    # Database
    DATABASE_URL: str

    # Security
    SECRET_KEY: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    EMAIL_VERIFICATION_EXPIRE_HOURS: int = 24
    PASSWORD_RESET_EXPIRE_HOURS: int = 1

    # CORS
    BACKEND_CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:3001"]

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v):
        """Parse CORS origins from string or list."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # Email
    RESEND_API_KEY: Optional[str] = None
    FROM_EMAIL: str = "<EMAIL>"

    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    AUTH_RATE_LIMIT_PER_MINUTE: int = 5

    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = Field(
        default_factory=lambda: os.getenv("DEBUG", "true").lower() == "true"
    )

    # Logging
    LOG_LEVEL_CONSOLE: str = Field(default="WARNING")  # WARNING, INFO, DEBUG
    LOG_LEVEL_FILE: str = Field(default="INFO")

    # Session settings
    SESSION_EXPIRE_HOURS: int = 24

    # Caching
    ENABLE_CACHING: bool = True
    CACHE_DEFAULT_TTL: int = 300  # 5 minutes
    CACHE_USER_TTL: int = 900  # 15 minutes
    CACHE_SESSION_TTL: int = 1800  # 30 minutes

    # Redis settings
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    REDIS_URL: Optional[str] = None

    @property
    def redis_url(self) -> str:
        """Get Redis URL for connection."""
        if self.REDIS_URL:
            return self.REDIS_URL

        auth = f":{self.REDIS_PASSWORD}@" if self.REDIS_PASSWORD else ""
        return f"redis://{auth}{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"


settings = Settings()
