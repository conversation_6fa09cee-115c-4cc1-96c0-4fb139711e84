"""User schemas."""
from pydantic import BaseModel, EmailStr
from typing import Optional

from app.schemas.base import BaseSchema


class UserBase(BaseModel):
    email: EmailStr
    full_name: Optional[str] = None
    is_active: bool = True


class UserCreate(UserBase):
    password: str


class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None


class UserResponse(BaseSchema, UserBase):
    is_verified: bool
    role: str