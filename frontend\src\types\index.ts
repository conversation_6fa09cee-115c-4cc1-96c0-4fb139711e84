// Global type definitions for the application

export interface User {
  id: string;
  email: string;
  full_name?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignupData {
  email: string;
  password: string;
  full_name?: string;
}

export interface ApiError {
  detail: string;
  status_code: number;
}

export interface ApiResponse<T = any> {
  data?: T;
  error?: ApiError;
  message?: string;
}

// Form state types for useActionState
export interface FormState {
  success: boolean;
  message?: string;
  errors?: Record<string, string[]>;
  tokens?: AuthTokens; // For login action
}

// Navigation types
export interface NavItem {
  title: string;
  url: string;
  icon?: React.ComponentType;
  isActive?: boolean;
  items?: NavItem[];
}

export interface Team {
  name: string;
  logo: React.ComponentType;
  plan: string;
}

export interface Project {
  name: string;
  url: string;
  icon: React.ComponentType;
}
