"""Authentication routes."""

from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status, Request, Response
from fastapi.security import OAuth2<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Form
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_async_db, get_current_user
from app.api.v1.schemas.token import Token, TokenPayload
from app.api.v1.schemas.user import User<PERSON>reate, UserLogin
from app.schemas.user import UserResponse
from app.core.security import (
    create_access_token,
    create_refresh_token,
    verify_token,
    get_password_hash,
)
from app.core.config import settings
from app.core.middleware import get_rate_limiter
from app.crud.user import user_crud
from app.crud.session import session_crud
from app.crud.email_verification import email_verification_crud
from app.models.user import User
from app.core.constants import TokenType, StatusMessage
from app.utils.email import email_service

router = APIRouter(prefix="/auth", tags=["Authentication"])
limiter = get_rate_limiter()


@router.post(
    "/register",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Register new user",
    description="Create a new user account with email verification",
)
@limiter.limit(f"{settings.AUTH_RATE_LIMIT_PER_MINUTE}/minute")
async def register(
    request: Request, user_data: UserCreate, db: AsyncSession = Depends(get_async_db)
):
    """
    Register a new user account.

    - **email**: Valid email address (will be verified)
    - **password**: Strong password (minimum 8 characters)
    - **full_name**: User's full name (optional)

    Returns the created user information and sends verification email.
    """
    # Check if user already exists
    existing_user = await user_crud.get_by_email_async(db, email=user_data.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Email already registered"
        )

    # Create new user
    user = await user_crud.create_async(db, obj_in=user_data)

    # Create email verification token
    verification = await email_verification_crud.create_verification(
        db,
        user_id=str(user.id),
        email=user.email,
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent"),
    )

    # Send verification email
    email_service.send_verification_email(
        email=user.email,
        verification_token=verification.token,
        user_name=user.full_name,
    )

    return user


@router.post(
    "/login",
    response_model=Token,
    summary="User login",
    description="Authenticate user and return access/refresh tokens",
)
@limiter.limit(f"{settings.AUTH_RATE_LIMIT_PER_MINUTE}/minute")
async def login(
    request: Request,
    response: Response,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_async_db),
):
    """
    Authenticate user and return JWT tokens.

    - **username**: User's email address
    - **password**: User's password

    Returns access token, refresh token, and sets session cookie.
    """
    # Authenticate user
    user = await user_crud.authenticate_async(
        db, email=form_data.username, password=form_data.password
    )

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user"
        )

    # Create tokens
    access_token = create_access_token(data={"sub": str(user.id)})
    refresh_token = create_refresh_token(data={"sub": str(user.id)})

    # Create session
    session = await session_crud.create_session(
        db,
        user_id=str(user.id),
        refresh_token=refresh_token,
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent"),
    )

    # Set session cookie
    response.set_cookie(
        key="session_token",
        value=session.session_token,
        max_age=settings.SESSION_EXPIRE_HOURS * 3600,
        httponly=True,
        secure=settings.ENVIRONMENT == "production",
        samesite="lax",
    )

    # Update last login
    await user_crud.update_last_login(db, user_id=str(user.id))

    return Token(
        access_token=access_token, refresh_token=refresh_token, token_type="bearer"
    )


@router.post("/refresh", response_model=Token)
async def refresh_token(
    request: Request, refresh_token: str, db: AsyncSession = Depends(get_async_db)
):
    """Refresh access token using refresh token."""
    # Verify refresh token
    payload = verify_token(refresh_token, "refresh")
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid refresh token"
        )

    user_id = payload.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid refresh token"
        )

    # Get session
    session = await session_crud.get_by_refresh_token(db, refresh_token=refresh_token)
    if not session or not session.is_valid():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired session",
        )

    # Create new access token
    new_access_token = create_access_token(data={"sub": user_id})

    # Update session last accessed
    await session_crud.update_last_accessed(db, session_id=str(session.id))

    return Token(
        access_token=new_access_token,
        refresh_token=refresh_token,  # Keep the same refresh token
        token_type="bearer",
    )


@router.post("/logout")
async def logout(
    request: Request,
    response: Response,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
):
    """User logout endpoint."""
    # Get session token from cookie
    session_token = request.cookies.get("session_token")

    if session_token:
        # Deactivate session
        await session_crud.deactivate_session(db, session_token=session_token)

    # Clear session cookie
    response.delete_cookie(key="session_token")

    return {"message": "Successfully logged out"}


@router.post("/logout-all")
async def logout_all(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
):
    """Logout from all devices."""
    # Deactivate all user sessions
    count = await session_crud.deactivate_user_sessions(
        db, user_id=str(current_user.id)
    )

    return {"message": f"Logged out from {count} devices"}


@router.post("/verify-email")
async def verify_email(token: str, db: AsyncSession = Depends(get_async_db)):
    """Verify user email with token."""
    # Get verification record
    verification = await email_verification_crud.get_by_token(db, token=token)

    if not verification or not verification.is_valid():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired verification token",
        )

    # Mark verification as used
    await email_verification_crud.mark_as_used(db, verification_id=str(verification.id))

    # Mark user as verified
    await user_crud.verify_email(db, user_id=str(verification.user_id))

    # Send welcome email
    user = await user_crud.get(db, id=str(verification.user_id))
    if user:
        email_service.send_welcome_email(email=user.email, user_name=user.full_name)

    return {"message": "Email verified successfully"}


@router.post("/resend-verification")
@limiter.limit(f"{settings.AUTH_RATE_LIMIT_PER_MINUTE}/minute")
async def resend_verification(
    request: Request, email: str, db: AsyncSession = Depends(get_async_db)
):
    """Resend email verification."""
    # Get user
    user = await user_crud.get_by_email_async(db, email=email)
    if not user:
        # Don't reveal if email exists
        return {"message": "If the email exists, a verification link has been sent"}

    if user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Email already verified"
        )

    # Invalidate existing verifications
    await email_verification_crud.invalidate_user_verifications(
        db, user_id=str(user.id), token_type=TokenType.EMAIL_VERIFICATION
    )

    # Create new verification
    verification = await email_verification_crud.create_verification(
        db,
        user_id=str(user.id),
        email=user.email,
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent"),
    )

    # Send verification email
    email_service.send_verification_email(
        email=user.email,
        verification_token=verification.token,
        user_name=user.full_name,
    )

    return {"message": "If the email exists, a verification link has been sent"}


@router.post("/forgot-password")
@limiter.limit(f"{settings.AUTH_RATE_LIMIT_PER_MINUTE}/minute")
async def forgot_password(
    request: Request, email: str, db: AsyncSession = Depends(get_async_db)
):
    """Request password reset."""
    # Get user
    user = await user_crud.get_by_email_async(db, email=email)
    if not user:
        # Don't reveal if email exists
        return {"message": "If the email exists, a password reset link has been sent"}

    if not user.is_active:
        return {"message": "If the email exists, a password reset link has been sent"}

    # Invalidate existing password reset tokens
    await email_verification_crud.invalidate_user_verifications(
        db, user_id=str(user.id), token_type=TokenType.PASSWORD_RESET
    )

    # Create password reset token
    reset_verification = await email_verification_crud.create_verification(
        db,
        user_id=str(user.id),
        email=user.email,
        token_type=TokenType.PASSWORD_RESET,
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent"),
    )

    # Send password reset email
    email_service.send_password_reset_email(
        email=user.email, reset_token=reset_verification.token, user_name=user.full_name
    )

    return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/reset-password")
@limiter.limit(f"{settings.AUTH_RATE_LIMIT_PER_MINUTE}/minute")
async def reset_password(
    request: Request,
    token: str,
    new_password: str,
    db: AsyncSession = Depends(get_async_db),
):
    """Reset password with token."""
    # Get password reset verification record
    verification = await email_verification_crud.get_by_token(db, token=token)

    if (
        not verification
        or verification.token_type != TokenType.PASSWORD_RESET
        or not verification.is_valid()
    ):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired password reset token",
        )

    # Get user
    user = await user_crud.get(db, id=str(verification.user_id))
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired password reset token",
        )

    # Update user password
    await user_crud.update_password(db, user_id=str(user.id), new_password=new_password)

    # Mark verification as used
    await email_verification_crud.mark_as_used(db, verification_id=str(verification.id))

    # Invalidate all user sessions (force re-login)
    await session_crud.deactivate_user_sessions(db, user_id=str(user.id))

    return {"message": "Password reset successfully"}
