import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Authentication - WebApp",
  description: "Sign in or create an account to access your dashboard",
};

interface AuthLayoutProps {
  children: React.ReactNode;
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-md">
        {children}
      </div>
    </div>
  );
}
