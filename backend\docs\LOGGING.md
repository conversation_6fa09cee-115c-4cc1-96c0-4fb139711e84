# Logging Configuration

This FastAPI application uses a sophisticated logging system that provides clean console output while maintaining detailed file logs.

## 🎯 Key Features

- **Clean Console**: Only shows important messages (warnings/errors) by default
- **Detailed File Logs**: All messages are saved to rotating log files
- **Configurable Levels**: Easy to adjust verbosity via environment variables
- **Multiple Log Files**: Separate files for general logs and errors
- **Production Ready**: JSON formatting and security logging for production

## 📁 Log Files

All logs are stored in the `logs/` directory:

```
logs/
├── app.log         # Main application logs (rotated, 10MB max, 5 backups)
├── error.log       # Error-level logs only
└── security.log    # Security events (production only)
```

## ⚙️ Configuration

### Environment Variables

```bash
# Console logging level (what you see in terminal)
LOG_LEVEL_CONSOLE=WARNING   # Default: only warnings and errors
LOG_LEVEL_CONSOLE=INFO      # Show info, warnings, and errors  
LOG_LEVEL_CONSOLE=DEBUG     # Show everything
LOG_LEVEL_CONSOLE=ERROR     # Show only errors

# File logging level (what gets saved to logs/)
LOG_LEVEL_FILE=INFO         # Default: info and above
LOG_LEVEL_FILE=DEBUG        # Save everything to file
```

### Quick Setup

1. **Development (clean console)**:
   ```bash
   export LOG_LEVEL_CONSOLE=WARNING
   export LOG_LEVEL_FILE=INFO
   ```

2. **Development (verbose console)**:
   ```bash
   export LOG_LEVEL_CONSOLE=INFO
   export LOG_LEVEL_FILE=DEBUG
   ```

3. **Production**:
   ```bash
   export LOG_LEVEL_CONSOLE=ERROR
   export LOG_LEVEL_FILE=INFO
   export ENVIRONMENT=production
   ```

## 🚀 Usage Examples

### Starting the Server (Clean Console)

```bash
# Clean console output - only startup messages and errors
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

Output:
```
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
00:32:44 - INFO - Started server process [18548]
00:32:44 - INFO - Application startup complete.
```

### Testing Logging Levels

```bash
python scripts/test_logging.py
```

### Viewing Logs

```bash
# View recent logs
Get-Content logs/app.log -Tail 20

# View error logs only
Get-Content logs/error.log -Tail 10

# Follow logs in real-time
Get-Content logs/app.log -Wait -Tail 10
```

## 📊 What Gets Logged Where

| Logger | Console (WARNING) | Console (INFO) | File |
|--------|------------------|----------------|------|
| App startup/shutdown | ✅ | ✅ | ✅ |
| API requests | ❌ | ❌ | ✅ |
| Database queries | ❌ | ❌ | ✅ |
| Cache operations | ❌ | ❌ | ✅ |
| Warnings | ✅ | ✅ | ✅ |
| Errors | ✅ | ✅ | ✅ |

## 🔧 Customization

### Adding Custom Loggers

```python
from app.core.logging import get_logger

logger = get_logger("my_module")
logger.info("This will go to file")
logger.warning("This will go to console and file")
```

### Temporary Verbose Mode

```bash
# Temporarily show all logs in console
LOG_LEVEL_CONSOLE=DEBUG uvicorn app.main:app --reload
```

## 🏭 Production Considerations

In production (`ENVIRONMENT=production`):

- Console logging is reduced to ERROR level only
- JSON formatting is used for structured logging
- Security events are logged to separate file
- Log rotation prevents disk space issues

## 🐛 Troubleshooting

### No logs appearing in console
- Check `LOG_LEVEL_CONSOLE` environment variable
- Ensure it's set to `INFO` or `DEBUG` for more verbose output

### Missing log files
- The `logs/` directory is created automatically
- Check file permissions if logs aren't being written

### Too much console output
- Set `LOG_LEVEL_CONSOLE=WARNING` or `LOG_LEVEL_CONSOLE=ERROR`
- All detailed logs will still be saved to files
