"""Performance monitoring and optimization utilities."""
import time
import asyncio
from typing import Callable, Dict, Any
from functools import wraps
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.logging import get_logger, log_api_request, performance_logger

logger = get_logger("performance")


class PerformanceMiddleware(BaseHTTPMiddleware):
    """Middleware to monitor API performance."""
    
    def __init__(self, app, slow_request_threshold: float = 1.0):
        super().__init__(app)
        self.slow_request_threshold = slow_request_threshold
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Monitor request performance."""
        start_time = time.time()
        
        # Get user ID if available
        user_id = getattr(request.state, "user_id", None)
        
        try:
            response = await call_next(request)
            
            # Calculate response time
            process_time = time.time() - start_time
            
            # Add performance headers
            response.headers["X-Process-Time"] = str(process_time)
            
            # Log API request
            log_api_request(
                method=request.method,
                path=str(request.url.path),
                user_id=user_id,
                response_time=process_time
            )
            
            # Log slow requests
            if process_time > self.slow_request_threshold:
                logger.warning(
                    f"Slow request: {request.method} {request.url.path} - {process_time:.3f}s",
                    extra={
                        "method": request.method,
                        "path": str(request.url.path),
                        "response_time": process_time,
                        "user_id": user_id,
                        "status_code": response.status_code,
                    }
                )
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(
                f"Request error: {request.method} {request.url.path} - {str(e)}",
                extra={
                    "method": request.method,
                    "path": str(request.url.path),
                    "response_time": process_time,
                    "user_id": user_id,
                    "error": str(e),
                },
                exc_info=True
            )
            raise


def monitor_performance(func_name: str = None):
    """Decorator to monitor function performance."""
    def decorator(func):
        name = func_name or f"{func.__module__}.{func.__name__}"
        
        if asyncio.iscoroutinefunction(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    
                    if execution_time > 0.5:  # Log functions taking more than 500ms
                        performance_logger.info(
                            f"Function execution: {name} - {execution_time:.3f}s",
                            extra={
                                "function": name,
                                "execution_time": execution_time,
                                "args_count": len(args),
                                "kwargs_count": len(kwargs),
                            }
                        )
                    
                    return result
                except Exception as e:
                    execution_time = time.time() - start_time
                    performance_logger.error(
                        f"Function error: {name} - {str(e)}",
                        extra={
                            "function": name,
                            "execution_time": execution_time,
                            "error": str(e),
                        },
                        exc_info=True
                    )
                    raise
            return async_wrapper
        else:
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    
                    if execution_time > 0.5:  # Log functions taking more than 500ms
                        performance_logger.info(
                            f"Function execution: {name} - {execution_time:.3f}s",
                            extra={
                                "function": name,
                                "execution_time": execution_time,
                                "args_count": len(args),
                                "kwargs_count": len(kwargs),
                            }
                        )
                    
                    return result
                except Exception as e:
                    execution_time = time.time() - start_time
                    performance_logger.error(
                        f"Function error: {name} - {str(e)}",
                        extra={
                            "function": name,
                            "execution_time": execution_time,
                            "error": str(e),
                        },
                        exc_info=True
                    )
                    raise
            return sync_wrapper
    return decorator


class MemoryMonitor:
    """Monitor memory usage."""
    
    @staticmethod
    def get_memory_usage() -> Dict[str, Any]:
        """Get current memory usage statistics."""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                "rss": memory_info.rss,  # Resident Set Size
                "vms": memory_info.vms,  # Virtual Memory Size
                "percent": process.memory_percent(),
                "available": psutil.virtual_memory().available,
                "total": psutil.virtual_memory().total,
            }
        except ImportError:
            logger.warning("psutil not available for memory monitoring")
            return {}
    
    @staticmethod
    def log_memory_usage():
        """Log current memory usage."""
        memory_stats = MemoryMonitor.get_memory_usage()
        if memory_stats:
            performance_logger.info(
                f"Memory usage: {memory_stats['percent']:.1f}%",
                extra=memory_stats
            )


class DatabasePerformanceMonitor:
    """Monitor database performance."""
    
    def __init__(self):
        self.query_count = 0
        self.total_query_time = 0.0
        self.slow_queries = []
    
    def record_query(self, query_time: float, query: str):
        """Record a database query for performance monitoring."""
        self.query_count += 1
        self.total_query_time += query_time
        
        if query_time > 1.0:  # Slow query threshold
            self.slow_queries.append({
                "query": query[:200],  # Truncate long queries
                "time": query_time,
                "timestamp": time.time(),
            })
            
            # Keep only recent slow queries
            if len(self.slow_queries) > 100:
                self.slow_queries = self.slow_queries[-50:]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get database performance statistics."""
        avg_query_time = self.total_query_time / self.query_count if self.query_count > 0 else 0
        
        return {
            "query_count": self.query_count,
            "total_query_time": self.total_query_time,
            "average_query_time": avg_query_time,
            "slow_query_count": len(self.slow_queries),
            "recent_slow_queries": self.slow_queries[-5:] if self.slow_queries else [],
        }
    
    def reset_stats(self):
        """Reset performance statistics."""
        self.query_count = 0
        self.total_query_time = 0.0
        self.slow_queries = []


# Global database performance monitor
db_performance_monitor = DatabasePerformanceMonitor()


def get_performance_stats() -> Dict[str, Any]:
    """Get comprehensive performance statistics."""
    return {
        "memory": MemoryMonitor.get_memory_usage(),
        "database": db_performance_monitor.get_stats(),
        "timestamp": time.time(),
    }


async def performance_health_check() -> Dict[str, Any]:
    """Perform a performance health check."""
    stats = get_performance_stats()
    
    # Determine health status based on metrics
    health_status = "healthy"
    issues = []
    
    # Check memory usage
    memory_stats = stats.get("memory", {})
    if memory_stats.get("percent", 0) > 90:
        health_status = "warning"
        issues.append("High memory usage")
    
    # Check database performance
    db_stats = stats.get("database", {})
    avg_query_time = db_stats.get("average_query_time", 0)
    if avg_query_time > 0.5:
        health_status = "warning"
        issues.append("Slow database queries")
    
    return {
        "status": health_status,
        "issues": issues,
        "stats": stats,
    }
